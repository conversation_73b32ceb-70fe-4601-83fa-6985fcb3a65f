<?php
/**
 * فئة قاعدة البيانات
 * Database Class for Classified Ads Website
 */

require_once 'config.php';

class Database {
    private static $instance = null;
    private $pdo;
    private $stmt;
    
    private function __construct() {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::ATTR_PERSISTENT => true
            ];
            
            $this->pdo = new PDO($dsn, DB_USER, DB_PASS, $options);
        } catch (PDOException $e) {
            logError("Database connection failed: " . $e->getMessage());
            die("خطأ في الاتصال بقاعدة البيانات");
        }
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    public function query($sql, $params = []) {
        try {
            $this->stmt = $this->pdo->prepare($sql);
            $this->stmt->execute($params);
            return $this;
        } catch (PDOException $e) {
            logError("Query failed: " . $e->getMessage() . " SQL: " . $sql);
            throw $e;
        }
    }
    
    public function fetch() {
        return $this->stmt->fetch();
    }
    
    public function fetchAll() {
        return $this->stmt->fetchAll();
    }
    
    public function fetchColumn() {
        return $this->stmt->fetchColumn();
    }
    
    public function rowCount() {
        return $this->stmt->rowCount();
    }
    
    public function lastInsertId() {
        return $this->pdo->lastInsertId();
    }
    
    public function beginTransaction() {
        return $this->pdo->beginTransaction();
    }
    
    public function commit() {
        return $this->pdo->commit();
    }
    
    public function rollback() {
        return $this->pdo->rollback();
    }
    
    // دالة للبحث مع الترقيم
    public function paginate($sql, $params = [], $page = 1, $perPage = 10) {
        $offset = ($page - 1) * $perPage;
        
        // حساب العدد الإجمالي
        $countSql = "SELECT COUNT(*) FROM ($sql) as count_table";
        $total = $this->query($countSql, $params)->fetchColumn();
        
        // جلب البيانات مع الحد
        $sql .= " LIMIT $perPage OFFSET $offset";
        $data = $this->query($sql, $params)->fetchAll();
        
        return [
            'data' => $data,
            'total' => $total,
            'page' => $page,
            'per_page' => $perPage,
            'total_pages' => ceil($total / $perPage),
            'has_next' => $page < ceil($total / $perPage),
            'has_prev' => $page > 1
        ];
    }
    
    // دالة للبحث النصي
    public function search($table, $columns, $term, $conditions = [], $params = []) {
        $searchColumns = implode(', ', $columns);
        $sql = "SELECT * FROM $table WHERE MATCH($searchColumns) AGAINST(? IN BOOLEAN MODE)";
        $searchParams = [$term];
        
        if (!empty($conditions)) {
            $sql .= " AND " . implode(' AND ', $conditions);
            $searchParams = array_merge($searchParams, $params);
        }
        
        return $this->query($sql, $searchParams);
    }
    
    // دالة لإدراج البيانات
    public function insert($table, $data) {
        $columns = implode(', ', array_keys($data));
        $placeholders = ':' . implode(', :', array_keys($data));
        $sql = "INSERT INTO $table ($columns) VALUES ($placeholders)";
        
        return $this->query($sql, $data);
    }
    
    // دالة لتحديث البيانات
    public function update($table, $data, $conditions, $params = []) {
        $set = [];
        foreach ($data as $key => $value) {
            $set[] = "$key = :$key";
        }
        $setClause = implode(', ', $set);
        
        $whereClause = implode(' AND ', $conditions);
        $sql = "UPDATE $table SET $setClause WHERE $whereClause";
        
        $allParams = array_merge($data, $params);
        return $this->query($sql, $allParams);
    }
    
    // دالة لحذف البيانات
    public function delete($table, $conditions, $params = []) {
        $whereClause = implode(' AND ', $conditions);
        $sql = "DELETE FROM $table WHERE $whereClause";
        
        return $this->query($sql, $params);
    }
    
    // دالة للحصول على سجل واحد
    public function find($table, $id, $column = 'id') {
        $sql = "SELECT * FROM $table WHERE $column = ? LIMIT 1";
        return $this->query($sql, [$id])->fetch();
    }
    
    // دالة للحصول على جميع السجلات
    public function findAll($table, $conditions = [], $params = [], $orderBy = null, $limit = null) {
        $sql = "SELECT * FROM $table";
        
        if (!empty($conditions)) {
            $sql .= " WHERE " . implode(' AND ', $conditions);
        }
        
        if ($orderBy) {
            $sql .= " ORDER BY $orderBy";
        }
        
        if ($limit) {
            $sql .= " LIMIT $limit";
        }
        
        return $this->query($sql, $params)->fetchAll();
    }
    
    // دالة للتحقق من وجود سجل
    public function exists($table, $conditions, $params = []) {
        $whereClause = implode(' AND ', $conditions);
        $sql = "SELECT COUNT(*) FROM $table WHERE $whereClause";
        
        return $this->query($sql, $params)->fetchColumn() > 0;
    }
    
    // دالة لحساب عدد السجلات
    public function count($table, $conditions = [], $params = []) {
        $sql = "SELECT COUNT(*) FROM $table";
        
        if (!empty($conditions)) {
            $sql .= " WHERE " . implode(' AND ', $conditions);
        }
        
        return $this->query($sql, $params)->fetchColumn();
    }
    
    // دالة للحصول على أقصى قيمة
    public function max($table, $column, $conditions = [], $params = []) {
        $sql = "SELECT MAX($column) FROM $table";
        
        if (!empty($conditions)) {
            $sql .= " WHERE " . implode(' AND ', $conditions);
        }
        
        return $this->query($sql, $params)->fetchColumn();
    }
    
    // دالة للحصول على أقل قيمة
    public function min($table, $column, $conditions = [], $params = []) {
        $sql = "SELECT MIN($column) FROM $table";
        
        if (!empty($conditions)) {
            $sql .= " WHERE " . implode(' AND ', $conditions);
        }
        
        return $this->query($sql, $params)->fetchColumn();
    }
    
    // دالة لحساب المتوسط
    public function avg($table, $column, $conditions = [], $params = []) {
        $sql = "SELECT AVG($column) FROM $table";
        
        if (!empty($conditions)) {
            $sql .= " WHERE " . implode(' AND ', $conditions);
        }
        
        return $this->query($sql, $params)->fetchColumn();
    }
    
    // دالة لحساب المجموع
    public function sum($table, $column, $conditions = [], $params = []) {
        $sql = "SELECT SUM($column) FROM $table";
        
        if (!empty($conditions)) {
            $sql .= " WHERE " . implode(' AND ', $conditions);
        }
        
        return $this->query($sql, $params)->fetchColumn();
    }
    
    // دالة لتنفيذ استعلام خام
    public function raw($sql, $params = []) {
        return $this->query($sql, $params);
    }
    
    // دالة للحصول على آخر خطأ
    public function getLastError() {
        return $this->pdo->errorInfo();
    }
    
    // دالة لإغلاق الاتصال
    public function close() {
        $this->pdo = null;
        $this->stmt = null;
    }
    
    // منع الاستنساخ
    private function __clone() {}
    
    // منع إلغاء التسلسل
    public function __wakeup() {
        throw new Exception("Cannot unserialize singleton");
    }
}

// فئة مساعدة للنماذج
abstract class Model {
    protected $db;
    protected $table;
    protected $primaryKey = 'id';
    protected $fillable = [];
    protected $hidden = [];
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    public function find($id) {
        return $this->db->find($this->table, $id, $this->primaryKey);
    }
    
    public function findAll($conditions = [], $params = [], $orderBy = null, $limit = null) {
        return $this->db->findAll($this->table, $conditions, $params, $orderBy, $limit);
    }
    
    public function create($data) {
        $filteredData = $this->filterFillable($data);
        $this->db->insert($this->table, $filteredData);
        return $this->db->lastInsertId();
    }
    
    public function update($id, $data) {
        $filteredData = $this->filterFillable($data);
        return $this->db->update($this->table, $filteredData, [$this->primaryKey . ' = ?'], [$id]);
    }
    
    public function delete($id) {
        return $this->db->delete($this->table, [$this->primaryKey . ' = ?'], [$id]);
    }
    
    public function exists($id) {
        return $this->db->exists($this->table, [$this->primaryKey . ' = ?'], [$id]);
    }
    
    public function count($conditions = [], $params = []) {
        return $this->db->count($this->table, $conditions, $params);
    }
    
    protected function filterFillable($data) {
        if (empty($this->fillable)) {
            return $data;
        }
        
        return array_intersect_key($data, array_flip($this->fillable));
    }
    
    protected function hideFields($data) {
        if (empty($this->hidden)) {
            return $data;
        }
        
        if (is_array($data) && isset($data[0])) {
            // مصفوفة من السجلات
            return array_map(function($record) {
                return array_diff_key($record, array_flip($this->hidden));
            }, $data);
        } else {
            // سجل واحد
            return array_diff_key($data, array_flip($this->hidden));
        }
    }
}

?>
