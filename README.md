# موقع الإعلانات المبوبة - Classified Ads Website

موقع إعلانات مبوبة شامل مطور بـ PHP مع لوحة تحكم إدارية متقدمة.

## المميزات الرئيسية

### للمستخدمين
- ✅ تصفح الإعلانات حسب الأقسام والمواقع
- ✅ البحث المتقدم مع فلاتر متعددة
- ✅ إضافة إعلانات جديدة مع رفع الصور
- ✅ إدارة الإعلانات الشخصية
- ✅ نظام المفضلة
- ✅ نظام الرسائل بين المستخدمين
- ✅ واجهة مستخدم عصرية ومتجاوبة

### للإدارة
- ✅ لوحة تحكم شاملة مع إحصائيات مفصلة
- ✅ إدارة الإعلانات (موافقة، رفض، تعديل)
- ✅ إدارة المستخدمين والصلاحيات
- ✅ إدارة الأقسام والأقسام الفرعية
- ✅ نظام التقارير والشكاوى
- ✅ إحصائيات مفصلة ورسوم بيانية
- ✅ إدارة الصفحات والمحتوى
- ✅ إعدادات الموقع المتقدمة

## التقنيات المستخدمة

- **Backend:** PHP 8.x
- **Database:** MySQL 8.x
- **Frontend:** HTML5, CSS3, JavaScript (ES6+)
- **Framework CSS:** Bootstrap 5.3
- **Icons:** Font Awesome 6
- **Charts:** Chart.js
- **Server:** Apache (XAMPP)

## متطلبات النظام

- PHP 8.0 أو أحدث
- MySQL 5.7 أو أحدث
- Apache Web Server
- إضافات PHP المطلوبة:
  - PDO
  - PDO_MySQL
  - GD
  - mbstring
  - openssl
  - curl

## التثبيت

### 1. تحضير البيئة

```bash
# تثبيت XAMPP
# تحميل من: https://www.apachefriends.org/

# تشغيل Apache و MySQL
```

### 2. تحميل الملفات

```bash
# نسخ ملفات المشروع إلى مجلد htdocs
cp -r classified-ads/ C:/xampp/htdocs/api/
```

### 3. إعداد قاعدة البيانات

```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE classified_ads CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- استيراد الهيكل
mysql -u root -p classified_ads < sql/database.sql
```

### 4. تكوين الإعدادات

```php
// تحديث ملف includes/config.php
define('DB_HOST', 'localhost');
define('DB_NAME', 'classified_ads');
define('DB_USER', 'root');
define('DB_PASS', '');

define('SITE_URL', 'http://localhost/api');
```

### 5. إعداد الصلاحيات

```bash
# إعطاء صلاحيات الكتابة لمجلدات الرفع
chmod 755 assets/uploads/
chmod 755 logs/
```

## الاستخدام

### الوصول للموقع
- **الموقع الرئيسي:** `http://localhost/api/`
- **لوحة الإدارة:** `http://localhost/api/admin/`

### بيانات الدخول الافتراضية للإدارة
- **اسم المستخدم:** admin
- **كلمة المرور:** password (يجب تغييرها فوراً)

## هيكل المشروع

```
api/
├── admin/                  # لوحة تحكم الإدارة
│   ├── includes/          # ملفات الإدارة المشتركة
│   ├── login.php          # تسجيل دخول الإدارة
│   ├── index.php          # لوحة التحكم الرئيسية
│   └── ...
├── assets/                # الملفات الثابتة
│   ├── css/              # ملفات التصميم
│   ├── js/               # ملفات JavaScript
│   ├── images/           # الصور
│   └── uploads/          # الملفات المرفوعة
├── includes/             # الملفات المشتركة
│   ├── config.php        # إعدادات الموقع
│   ├── database.php      # فئة قاعدة البيانات
│   ├── functions.php     # الوظائف المساعدة
│   ├── models.php        # نماذج البيانات
│   ├── header.php        # هيدر الموقع
│   ├── footer.php        # فوتر الموقع
│   └── ad-card.php       # بطاقة الإعلان
├── sql/                  # ملفات قاعدة البيانات
│   └── database.sql      # هيكل قاعدة البيانات
├── index.php             # الصفحة الرئيسية
├── ads.php               # صفحة الإعلانات
├── .htaccess             # إعدادات Apache
└── README.md             # هذا الملف
```

## الميزات المتقدمة

### نظام الأمان
- حماية من هجمات SQL Injection
- حماية من هجمات XSS
- حماية من هجمات CSRF
- تشفير كلمات المرور
- جلسات آمنة

### الأداء
- تحسين استعلامات قاعدة البيانات
- ضغط الملفات
- تخزين مؤقت للصور
- تحميل تدريجي للمحتوى

### SEO
- URLs صديقة لمحركات البحث
- Meta tags محسنة
- Sitemap تلقائي
- Schema markup

## التخصيص

### إضافة أقسام جديدة
```php
// من خلال لوحة الإدارة أو مباشرة في قاعدة البيانات
INSERT INTO categories (name_ar, name_en, icon, status) 
VALUES ('قسم جديد', 'New Category', 'fas fa-icon', 'active');
```

### تخصيص التصميم
```css
/* تحديث ملف assets/css/style.css */
:root {
    --primary-color: #your-color;
    --secondary-color: #your-color;
}
```

### إضافة حقول جديدة للإعلانات
```sql
-- إضافة حقل جديد لجدول الإعلانات
ALTER TABLE ads ADD COLUMN new_field VARCHAR(255) DEFAULT NULL;
```

## الصيانة

### النسخ الاحتياطي
```bash
# نسخ احتياطي لقاعدة البيانات
mysqldump -u root -p classified_ads > backup_$(date +%Y%m%d).sql

# نسخ احتياطي للملفات المرفوعة
tar -czf uploads_backup_$(date +%Y%m%d).tar.gz assets/uploads/
```

### تحديث الموقع
```bash
# نسخ احتياطي أولاً
# ثم نسخ الملفات الجديدة
# تشغيل سكريبت التحديث إذا وجد
```

## استكشاف الأخطاء

### مشاكل شائعة

1. **خطأ في الاتصال بقاعدة البيانات**
   - تحقق من إعدادات قاعدة البيانات في config.php
   - تأكد من تشغيل MySQL

2. **مشاكل في رفع الملفات**
   - تحقق من صلاحيات مجلد uploads
   - تحقق من إعدادات PHP (upload_max_filesize)

3. **مشاكل في التصميم**
   - تحقق من تحميل ملفات CSS و JavaScript
   - تحقق من مسارات الملفات

### تفعيل وضع التطوير
```php
// في ملف config.php
define('DEBUG_MODE', true);
define('LOG_ERRORS', true);
```

## المساهمة

نرحب بالمساهمات! يرجى:

1. Fork المشروع
2. إنشاء branch جديد للميزة
3. Commit التغييرات
4. Push إلى Branch
5. إنشاء Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## الدعم

للحصول على الدعم:
- إنشاء Issue في GitHub
- مراسلة البريد الإلكتروني: <EMAIL>
- زيارة الوثائق: [docs.example.com](https://docs.example.com)

## الإصدارات

### v1.0.0 (الحالي)
- إطلاق أولي مع جميع الميزات الأساسية
- لوحة تحكم إدارية كاملة
- نظام إدارة الإعلانات
- نظام المستخدمين والصلاحيات

### خطط مستقبلية
- [ ] تطبيق موبايل
- [ ] نظام الدفع الإلكتروني
- [ ] تكامل مع وسائل التواصل الاجتماعي
- [ ] نظام التقييمات والمراجعات
- [ ] دعم متعدد اللغات

---

**تم تطوير هذا المشروع بواسطة:** فريق التطوير
**تاريخ آخر تحديث:** 2025-01-04
