<?php
/**
 * نماذج البيانات
 * Data Models for Classified Ads Website
 */

require_once 'database.php';

/**
 * نموذج المستخدم
 */
class User extends Model {
    protected $table = 'users';
    protected $fillable = [
        'username', 'email', 'password', 'full_name', 'phone', 
        'avatar', 'user_type', 'status', 'email_verified'
    ];
    protected $hidden = ['password', 'verification_token', 'reset_token'];
    
    public function getAds($status = null) {
        $conditions = ['user_id = ?'];
        $params = [$this->id];
        
        if ($status) {
            $conditions[] = 'status = ?';
            $params[] = $status;
        }
        
        return $this->db->findAll('ads', $conditions, $params, 'created_at DESC');
    }
    
    public function getFavorites() {
        $sql = "
            SELECT a.*, c.name_ar as category_name
            FROM favorites f
            JOIN ads a ON f.ad_id = a.id
            JOIN categories c ON a.category_id = c.id
            WHERE f.user_id = ? AND a.status = 'active'
            ORDER BY f.created_at DESC
        ";
        
        return $this->db->query($sql, [$this->id])->fetchAll();
    }
    
    public function addToFavorites($adId) {
        try {
            $this->db->insert('favorites', [
                'user_id' => $this->id,
                'ad_id' => $adId
            ]);
            
            // زيادة عداد المفضلة في الإعلان
            $this->db->query("UPDATE ads SET favorites = favorites + 1 WHERE id = ?", [$adId]);
            
            return true;
        } catch (Exception $e) {
            return false;
        }
    }
    
    public function removeFromFavorites($adId) {
        $result = $this->db->delete('favorites', ['user_id = ? AND ad_id = ?'], [$this->id, $adId]);
        
        if ($result->rowCount() > 0) {
            // تقليل عداد المفضلة في الإعلان
            $this->db->query("UPDATE ads SET favorites = GREATEST(favorites - 1, 0) WHERE id = ?", [$adId]);
            return true;
        }
        
        return false;
    }
    
    public function isFavorite($adId) {
        return $this->db->exists('favorites', ['user_id = ? AND ad_id = ?'], [$this->id, $adId]);
    }
    
    public function getMessages($type = 'received') {
        $column = $type === 'sent' ? 'sender_id' : 'receiver_id';
        
        $sql = "
            SELECT m.*, a.title as ad_title, u.full_name as other_user_name
            FROM messages m
            JOIN ads a ON m.ad_id = a.id
            JOIN users u ON " . ($type === 'sent' ? 'm.receiver_id' : 'm.sender_id') . " = u.id
            WHERE m.$column = ?
            ORDER BY m.created_at DESC
        ";
        
        return $this->db->query($sql, [$this->id])->fetchAll();
    }
    
    public function sendMessage($receiverId, $adId, $subject, $message) {
        return $this->db->insert('messages', [
            'sender_id' => $this->id,
            'receiver_id' => $receiverId,
            'ad_id' => $adId,
            'subject' => $subject,
            'message' => $message
        ]);
    }
    
    public function getStatistics() {
        $stats = [];
        
        // عدد الإعلانات
        $stats['total_ads'] = $this->db->count('ads', ['user_id = ?'], [$this->id]);
        $stats['active_ads'] = $this->db->count('ads', ['user_id = ? AND status = ?'], [$this->id, 'active']);
        $stats['pending_ads'] = $this->db->count('ads', ['user_id = ? AND status = ?'], [$this->id, 'pending']);
        
        // إجمالي المشاهدات
        $stats['total_views'] = $this->db->query(
            "SELECT SUM(views) FROM ads WHERE user_id = ?", 
            [$this->id]
        )->fetchColumn() ?: 0;
        
        // عدد المفضلة
        $stats['total_favorites'] = $this->db->count('favorites', ['user_id = ?'], [$this->id]);
        
        // عدد الرسائل غير المقروءة
        $stats['unread_messages'] = $this->db->count('messages', 
            ['receiver_id = ? AND is_read = ?'], 
            [$this->id, 0]
        );
        
        return $stats;
    }
}

/**
 * نموذج الإعلان
 */
class Ad extends Model {
    protected $table = 'ads';
    protected $fillable = [
        'user_id', 'category_id', 'title', 'description', 'price', 'price_type',
        'location', 'address', 'contact_name', 'contact_phone', 'contact_email',
        'whatsapp', 'featured', 'urgent', 'status'
    ];
    
    public function getUser() {
        return $this->db->find('users', $this->user_id);
    }
    
    public function getCategory() {
        return $this->db->find('categories', $this->category_id);
    }
    
    public function getImages() {
        return $this->db->findAll('ad_images', ['ad_id = ?'], [$this->id], 'sort_order ASC');
    }
    
    public function getPrimaryImage() {
        $image = $this->db->query(
            "SELECT * FROM ad_images WHERE ad_id = ? AND is_primary = 1 LIMIT 1",
            [$this->id]
        )->fetch();
        
        if (!$image) {
            $image = $this->db->query(
                "SELECT * FROM ad_images WHERE ad_id = ? ORDER BY sort_order LIMIT 1",
                [$this->id]
            )->fetch();
        }
        
        return $image;
    }
    
    public function addImage($imagePath, $imageName, $isPrimary = false, $sortOrder = 0) {
        return $this->db->insert('ad_images', [
            'ad_id' => $this->id,
            'image_path' => $imagePath,
            'image_name' => $imageName,
            'is_primary' => $isPrimary ? 1 : 0,
            'sort_order' => $sortOrder
        ]);
    }
    
    public function removeImage($imageId) {
        $image = $this->db->find('ad_images', $imageId);
        if ($image && $image['ad_id'] == $this->id) {
            // حذف الملف من الخادم
            $imagePath = UPLOAD_PATH . $image['image_path'];
            if (file_exists($imagePath)) {
                unlink($imagePath);
            }
            
            return $this->db->delete('ad_images', ['id = ?'], [$imageId]);
        }
        
        return false;
    }
    
    public function getSimilarAds($limit = 5) {
        $sql = "
            SELECT a.*, c.name_ar as category_name
            FROM ads a
            JOIN categories c ON a.category_id = c.id
            WHERE a.category_id = ? AND a.id != ? AND a.status = 'active'
            ORDER BY a.featured DESC, a.created_at DESC
            LIMIT ?
        ";
        
        return $this->db->query($sql, [$this->category_id, $this->id, $limit])->fetchAll();
    }
    
    public function incrementViews() {
        return $this->db->query("UPDATE ads SET views = views + 1 WHERE id = ?", [$this->id]);
    }
    
    public function isExpired() {
        return $this->expires_at && strtotime($this->expires_at) < time();
    }
    
    public function isFeatured() {
        return $this->featured && 
               ($this->featured_until === null || strtotime($this->featured_until) > time());
    }
    
    public function makeFeatured($days = 30) {
        $featuredUntil = date('Y-m-d H:i:s', strtotime("+$days days"));
        
        return $this->db->update('ads', 
            ['featured' => 1, 'featured_until' => $featuredUntil],
            ['id = ?'],
            [$this->id]
        );
    }
    
    public function approve() {
        return $this->db->update('ads', 
            ['status' => 'active'],
            ['id = ?'],
            [$this->id]
        );
    }
    
    public function reject($reason = null) {
        return $this->db->update('ads', 
            ['status' => 'rejected', 'rejection_reason' => $reason],
            ['id = ?'],
            [$this->id]
        );
    }
    
    public function markAsSold() {
        return $this->db->update('ads', 
            ['status' => 'sold'],
            ['id = ?'],
            [$this->id]
        );
    }
    
    public function extend($days = 30) {
        $newExpiry = date('Y-m-d H:i:s', strtotime("+$days days"));
        
        return $this->db->update('ads', 
            ['expires_at' => $newExpiry],
            ['id = ?'],
            [$this->id]
        );
    }
}

/**
 * نموذج القسم
 */
class Category extends Model {
    protected $table = 'categories';
    protected $fillable = [
        'name_ar', 'name_en', 'description', 'icon', 'image', 
        'parent_id', 'sort_order', 'status', 'meta_title', 'meta_description'
    ];
    
    public function getParent() {
        if ($this->parent_id) {
            return $this->db->find('categories', $this->parent_id);
        }
        return null;
    }
    
    public function getChildren() {
        return $this->db->findAll('categories', 
            ['parent_id = ? AND status = ?'], 
            [$this->id, 'active'], 
            'sort_order ASC'
        );
    }
    
    public function getAds($status = 'active', $limit = null) {
        $conditions = ['category_id = ?'];
        $params = [$this->id];
        
        if ($status) {
            $conditions[] = 'status = ?';
            $params[] = $status;
        }
        
        return $this->db->findAll('ads', $conditions, $params, 'created_at DESC', $limit);
    }
    
    public function getAdsCount($status = 'active') {
        $conditions = ['category_id = ?'];
        $params = [$this->id];
        
        if ($status) {
            $conditions[] = 'status = ?';
            $params[] = $status;
        }
        
        return $this->db->count('ads', $conditions, $params);
    }
    
    public function isParent() {
        return $this->parent_id === null;
    }
    
    public function hasChildren() {
        return $this->db->exists('categories', ['parent_id = ?'], [$this->id]);
    }
    
    public function getBreadcrumb() {
        $breadcrumb = [];
        $current = $this;
        
        while ($current) {
            array_unshift($breadcrumb, $current);
            $current = $current->getParent();
        }
        
        return $breadcrumb;
    }
}

/**
 * نموذج المدير
 */
class Admin extends Model {
    protected $table = 'admins';
    protected $fillable = [
        'username', 'email', 'password', 'full_name', 'role', 
        'permissions', 'avatar', 'status'
    ];
    protected $hidden = ['password'];
    
    public function hasPermission($permission) {
        if ($this->role === 'super_admin') {
            return true;
        }
        
        $permissions = json_decode($this->permissions, true) ?: [];
        return in_array($permission, $permissions);
    }
    
    public function canManageUsers() {
        return $this->hasPermission('manage_users') || $this->role === 'super_admin';
    }
    
    public function canManageAds() {
        return $this->hasPermission('manage_ads') || $this->role !== 'moderator';
    }
    
    public function canManageCategories() {
        return $this->hasPermission('manage_categories') || $this->role === 'super_admin';
    }
    
    public function canManageSettings() {
        return $this->role === 'super_admin';
    }
    
    public function getStatistics() {
        $stats = [];
        
        // إحصائيات الإعلانات
        $stats['total_ads'] = $this->db->count('ads');
        $stats['active_ads'] = $this->db->count('ads', ['status = ?'], ['active']);
        $stats['pending_ads'] = $this->db->count('ads', ['status = ?'], ['pending']);
        $stats['rejected_ads'] = $this->db->count('ads', ['status = ?'], ['rejected']);
        
        // إحصائيات المستخدمين
        $stats['total_users'] = $this->db->count('users');
        $stats['active_users'] = $this->db->count('users', ['status = ?'], ['active']);
        $stats['new_users_today'] = $this->db->count('users', 
            ['DATE(created_at) = ?'], 
            [date('Y-m-d')]
        );
        
        // إحصائيات الأقسام
        $stats['total_categories'] = $this->db->count('categories');
        $stats['active_categories'] = $this->db->count('categories', ['status = ?'], ['active']);
        
        // إحصائيات المشاهدات
        $stats['total_views'] = $this->db->query("SELECT SUM(views) FROM ads")->fetchColumn() ?: 0;
        $stats['views_today'] = $this->db->count('statistics', 
            ['action_type = ? AND DATE(created_at) = ?'], 
            ['view', date('Y-m-d')]
        );
        
        return $stats;
    }
}

/**
 * نموذج الرسالة
 */
class Message extends Model {
    protected $table = 'messages';
    protected $fillable = [
        'ad_id', 'sender_id', 'receiver_id', 'subject', 'message'
    ];
    
    public function getSender() {
        return $this->db->find('users', $this->sender_id);
    }
    
    public function getReceiver() {
        return $this->db->find('users', $this->receiver_id);
    }
    
    public function getAd() {
        return $this->db->find('ads', $this->ad_id);
    }
    
    public function markAsRead() {
        return $this->db->update('messages', 
            ['is_read' => 1],
            ['id = ?'],
            [$this->id]
        );
    }
    
    public function isRead() {
        return $this->is_read == 1;
    }
}

/**
 * نموذج الإحصائيات
 */
class Statistic extends Model {
    protected $table = 'statistics';
    protected $fillable = [
        'ad_id', 'user_id', 'action_type', 'ip_address', 
        'user_agent', 'referrer', 'search_query'
    ];
    
    public static function record($adId, $actionType, $userId = null, $searchQuery = null) {
        $db = Database::getInstance();
        
        $data = [
            'ad_id' => $adId,
            'user_id' => $userId,
            'action_type' => $actionType,
            'ip_address' => getRealIP(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'referrer' => $_SERVER['HTTP_REFERER'] ?? '',
            'search_query' => $searchQuery
        ];
        
        try {
            return $db->insert('statistics', $data);
        } catch (Exception $e) {
            return false;
        }
    }
    
    public static function getPopularAds($limit = 10, $days = 30) {
        $db = Database::getInstance();
        
        $sql = "
            SELECT a.id, a.title, COUNT(s.id) as view_count
            FROM ads a
            JOIN statistics s ON a.id = s.ad_id
            WHERE s.action_type = 'view' 
            AND s.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
            AND a.status = 'active'
            GROUP BY a.id, a.title
            ORDER BY view_count DESC
            LIMIT ?
        ";
        
        return $db->query($sql, [$days, $limit])->fetchAll();
    }
    
    public static function getSearchStats($days = 30) {
        $db = Database::getInstance();
        
        $sql = "
            SELECT search_query, COUNT(*) as search_count
            FROM statistics
            WHERE action_type = 'search' 
            AND search_query IS NOT NULL
            AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
            GROUP BY search_query
            ORDER BY search_count DESC
            LIMIT 20
        ";
        
        return $db->query($sql, [$days])->fetchAll();
    }
}

?>
