<?php
/**
 * ملف التحقق من صلاحيات الإدارة
 * Admin Authentication and Authorization
 */

require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/models.php';

// بدء الجلسة إذا لم تكن مبدوءة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

/**
 * دالة تسجيل دخول المدير
 */
function adminLogin($username, $password) {
    $db = Database::getInstance();
    
    // البحث عن المدير
    $admin = $db->query("SELECT * FROM admins WHERE username = ? AND status = 'active'", [$username])->fetch();
    
    if ($admin && verifyPassword($password, $admin['password'])) {
        // تحديث آخر تسجيل دخول
        $db->query("UPDATE admins SET last_login = NOW() WHERE id = ?", [$admin['id']]);
        
        // حفظ بيانات المدير في الجلسة
        $_SESSION['admin_id'] = $admin['id'];
        $_SESSION['admin_username'] = $admin['username'];
        $_SESSION['admin_name'] = $admin['full_name'];
        $_SESSION['admin_role'] = $admin['role'];
        $_SESSION['admin_permissions'] = json_decode($admin['permissions'], true) ?: [];
        $_SESSION['admin_logged_in'] = true;
        
        // تسجيل محاولة الدخول الناجحة
        logAdminActivity($admin['id'], 'login', 'تسجيل دخول ناجح');
        
        return true;
    }
    
    // تسجيل محاولة الدخول الفاشلة
    logAdminActivity(null, 'login_failed', "محاولة دخول فاشلة للمستخدم: $username");
    
    return false;
}

/**
 * دالة تسجيل خروج المدير
 */
function adminLogout() {
    if (isAdminLoggedIn()) {
        logAdminActivity($_SESSION['admin_id'], 'logout', 'تسجيل خروج');
    }
    
    // إزالة جميع متغيرات الجلسة الخاصة بالإدارة
    unset($_SESSION['admin_id']);
    unset($_SESSION['admin_username']);
    unset($_SESSION['admin_name']);
    unset($_SESSION['admin_role']);
    unset($_SESSION['admin_permissions']);
    unset($_SESSION['admin_logged_in']);
    
    return true;
}

/**
 * دالة للتحقق من تسجيل دخول المدير
 */
function isAdminLoggedIn() {
    return isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true;
}

/**
 * دالة للحصول على المدير الحالي
 */
function getCurrentAdmin() {
    if (!isAdminLoggedIn()) {
        return null;
    }
    
    $db = Database::getInstance();
    return $db->find('admins', $_SESSION['admin_id']);
}

/**
 * دالة للتحقق من الصلاحيات
 */
function hasAdminPermission($permission) {
    if (!isAdminLoggedIn()) {
        return false;
    }
    
    // المدير الأعلى له جميع الصلاحيات
    if ($_SESSION['admin_role'] === 'super_admin') {
        return true;
    }
    
    // التحقق من الصلاحيات المحددة
    $permissions = $_SESSION['admin_permissions'] ?? [];
    return in_array($permission, $permissions);
}

/**
 * دالة للتحقق من الدور
 */
function hasAdminRole($role) {
    if (!isAdminLoggedIn()) {
        return false;
    }
    
    if (is_array($role)) {
        return in_array($_SESSION['admin_role'], $role);
    }
    
    return $_SESSION['admin_role'] === $role;
}

/**
 * دالة لطلب تسجيل الدخول
 */
function requireAdminLogin() {
    if (!isAdminLoggedIn()) {
        $_SESSION['admin_redirect_url'] = $_SERVER['REQUEST_URI'];
        redirect(url('admin/login.php'));
    }
}

/**
 * دالة لطلب صلاحية معينة
 */
function requireAdminPermission($permission) {
    requireAdminLogin();
    
    if (!hasAdminPermission($permission)) {
        $_SESSION['error'] = 'ليس لديك صلاحية للوصول لهذه الصفحة';
        redirect(url('admin/index.php'));
    }
}

/**
 * دالة لطلب دور معين
 */
function requireAdminRole($role) {
    requireAdminLogin();
    
    if (!hasAdminRole($role)) {
        $_SESSION['error'] = 'ليس لديك صلاحية للوصول لهذه الصفحة';
        redirect(url('admin/index.php'));
    }
}

/**
 * دالة لتسجيل أنشطة المدير
 */
function logAdminActivity($adminId, $action, $description, $targetType = null, $targetId = null) {
    try {
        $db = Database::getInstance();
        
        $data = [
            'admin_id' => $adminId,
            'action' => $action,
            'description' => $description,
            'target_type' => $targetType,
            'target_id' => $targetId,
            'ip_address' => getRealIP(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        // إنشاء جدول سجل الأنشطة إذا لم يكن موجوداً
        $db->query("
            CREATE TABLE IF NOT EXISTS admin_activity_log (
                id INT AUTO_INCREMENT PRIMARY KEY,
                admin_id INT DEFAULT NULL,
                action VARCHAR(100) NOT NULL,
                description TEXT,
                target_type VARCHAR(50) DEFAULT NULL,
                target_id INT DEFAULT NULL,
                ip_address VARCHAR(45),
                user_agent TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_admin_id (admin_id),
                INDEX idx_action (action),
                INDEX idx_created_at (created_at)
            )
        ");
        
        $db->insert('admin_activity_log', $data);
        
    } catch (Exception $e) {
        // تجاهل أخطاء تسجيل الأنشطة
        error_log("خطأ في تسجيل نشاط المدير: " . $e->getMessage());
    }
}

/**
 * دالة للحصول على قائمة الصلاحيات المتاحة
 */
function getAvailablePermissions() {
    return [
        'manage_ads' => 'إدارة الإعلانات',
        'manage_users' => 'إدارة المستخدمين',
        'manage_categories' => 'إدارة الأقسام',
        'manage_pages' => 'إدارة الصفحات',
        'manage_settings' => 'إدارة الإعدادات',
        'view_statistics' => 'عرض الإحصائيات',
        'manage_admins' => 'إدارة المديرين',
        'manage_reports' => 'إدارة التقارير',
        'manage_messages' => 'إدارة الرسائل',
        'backup_restore' => 'النسخ الاحتياطي والاستعادة'
    ];
}

/**
 * دالة للحصول على قائمة الأدوار المتاحة
 */
function getAvailableRoles() {
    return [
        'super_admin' => 'مدير أعلى',
        'admin' => 'مدير',
        'moderator' => 'مشرف'
    ];
}

/**
 * دالة لتنظيف جلسات المديرين المنتهية الصلاحية
 */
function cleanupAdminSessions() {
    // يمكن تنفيذ هذه الدالة بواسطة cron job
    // لحذف الجلسات القديمة من قاعدة البيانات
}

/**
 * دالة للتحقق من قوة كلمة المرور
 */
function validateAdminPassword($password) {
    $errors = [];
    
    if (strlen($password) < 8) {
        $errors[] = 'كلمة المرور يجب أن تكون 8 أحرف على الأقل';
    }
    
    if (!preg_match('/[A-Z]/', $password)) {
        $errors[] = 'كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل';
    }
    
    if (!preg_match('/[a-z]/', $password)) {
        $errors[] = 'كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل';
    }
    
    if (!preg_match('/[0-9]/', $password)) {
        $errors[] = 'كلمة المرور يجب أن تحتوي على رقم واحد على الأقل';
    }
    
    if (!preg_match('/[^A-Za-z0-9]/', $password)) {
        $errors[] = 'كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل';
    }
    
    return $errors;
}

/**
 * دالة لإنشاء رمز CSRF خاص بالإدارة
 */
function generateAdminCSRFToken() {
    if (!isset($_SESSION['admin_csrf_token'])) {
        $_SESSION['admin_csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['admin_csrf_token'];
}

/**
 * دالة للتحقق من رمز CSRF الخاص بالإدارة
 */
function verifyAdminCSRFToken($token) {
    return isset($_SESSION['admin_csrf_token']) && hash_equals($_SESSION['admin_csrf_token'], $token);
}

/**
 * دالة لعرض رسائل التنبيه في لوحة الإدارة
 */
function showAdminAlert($message, $type = 'info') {
    $alertClass = [
        'success' => 'alert-success',
        'error' => 'alert-danger',
        'warning' => 'alert-warning',
        'info' => 'alert-info'
    ];
    
    $class = $alertClass[$type] ?? 'alert-info';
    $icon = [
        'success' => 'fas fa-check-circle',
        'error' => 'fas fa-exclamation-triangle',
        'warning' => 'fas fa-exclamation-circle',
        'info' => 'fas fa-info-circle'
    ];
    
    $iconClass = $icon[$type] ?? 'fas fa-info-circle';
    
    return "<div class='alert $class alert-dismissible fade show' role='alert'>
                <i class='$iconClass me-2'></i>
                $message
                <button type='button' class='btn-close' data-bs-dismiss='alert'></button>
            </div>";
}

/**
 * دالة لعرض رسائل الجلسة في لوحة الإدارة
 */
function showAdminSessionMessages() {
    $output = '';
    
    if (isset($_SESSION['admin_success'])) {
        $output .= showAdminAlert($_SESSION['admin_success'], 'success');
        unset($_SESSION['admin_success']);
    }
    
    if (isset($_SESSION['admin_error'])) {
        $output .= showAdminAlert($_SESSION['admin_error'], 'error');
        unset($_SESSION['admin_error']);
    }
    
    if (isset($_SESSION['admin_warning'])) {
        $output .= showAdminAlert($_SESSION['admin_warning'], 'warning');
        unset($_SESSION['admin_warning']);
    }
    
    if (isset($_SESSION['admin_info'])) {
        $output .= showAdminAlert($_SESSION['admin_info'], 'info');
        unset($_SESSION['admin_info']);
    }
    
    return $output;
}

/**
 * دالة للحصول على إحصائيات سريعة للوحة الإدارة
 */
function getAdminDashboardStats() {
    $db = Database::getInstance();
    
    $stats = [
        // إحصائيات الإعلانات
        'total_ads' => $db->count('ads'),
        'active_ads' => $db->count('ads', ['status = ?'], ['active']),
        'pending_ads' => $db->count('ads', ['status = ?'], ['pending']),
        'rejected_ads' => $db->count('ads', ['status = ?'], ['rejected']),
        'featured_ads' => $db->count('ads', ['featured = ?'], [1]),
        
        // إحصائيات المستخدمين
        'total_users' => $db->count('users'),
        'active_users' => $db->count('users', ['status = ?'], ['active']),
        'new_users_today' => $db->count('users', ['DATE(created_at) = ?'], [date('Y-m-d')]),
        'new_users_week' => $db->count('users', ['created_at >= ?'], [date('Y-m-d', strtotime('-7 days'))]),
        
        // إحصائيات الأقسام
        'total_categories' => $db->count('categories'),
        'active_categories' => $db->count('categories', ['status = ?'], ['active']),
        
        // إحصائيات المشاهدات
        'total_views' => $db->query("SELECT SUM(views) FROM ads")->fetchColumn() ?: 0,
        'views_today' => $db->count('statistics', ['action_type = ? AND DATE(created_at) = ?'], ['view', date('Y-m-d')]),
        
        // إحصائيات الرسائل
        'total_messages' => $db->count('messages'),
        'unread_messages' => $db->count('messages', ['is_read = ?'], [0]),
        
        // إحصائيات التقارير
        'total_reports' => $db->count('reports'),
        'pending_reports' => $db->count('reports', ['status = ?'], ['pending'])
    ];
    
    return $stats;
}

/**
 * دالة لتنسيق URL لوحة الإدارة
 */
function adminUrl($path = '') {
    return url('admin/' . ltrim($path, '/'));
}

/**
 * دالة للتحقق من الوصول الآمن للملفات
 */
function checkAdminAccess() {
    // التحقق من أن الطلب يأتي من نطاق صحيح
    $allowedHosts = ['localhost', '127.0.0.1', $_SERVER['SERVER_NAME']];
    $currentHost = $_SERVER['HTTP_HOST'] ?? '';
    
    if (!in_array($currentHost, $allowedHosts)) {
        die('غير مسموح بالوصول');
    }
    
    // التحقق من User Agent (اختياري)
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    if (empty($userAgent) || strlen($userAgent) < 10) {
        // يمكن تسجيل هذا كمحاولة مشبوهة
        logAdminActivity(null, 'suspicious_access', 'محاولة وصول بدون User Agent صحيح');
    }
}

// تشغيل فحص الوصول الآمن
checkAdminAccess();

?>
