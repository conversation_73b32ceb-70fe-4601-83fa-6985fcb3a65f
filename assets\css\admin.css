/* 
 * ملف CSS خاص بلوحة الإدارة
 * Admin Panel CSS File
 */

/* إعدادات عامة */
:root {
    --admin-primary: #2c3e50;
    --admin-secondary: #34495e;
    --admin-accent: #3498db;
    --admin-success: #27ae60;
    --admin-warning: #f39c12;
    --admin-danger: #e74c3c;
    --admin-info: #17a2b8;
    --admin-light: #ecf0f1;
    --admin-dark: #2c3e50;
    --admin-sidebar-width: 280px;
    --admin-header-height: 70px;
    --admin-border-radius: 8px;
    --admin-box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    --admin-transition: all 0.3s ease;
}

/* تخطيط عام */
.admin-body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    margin: 0;
    padding: 0;
}

.admin-wrapper {
    display: flex;
    min-height: 100vh;
    padding-top: var(--admin-header-height);
}

/* Header الإدارة */
.admin-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: var(--admin-header-height);
    background: linear-gradient(135deg, var(--admin-primary), var(--admin-secondary));
    color: white;
    z-index: 1000;
    box-shadow: var(--admin-box-shadow);
}

.admin-header .navbar {
    padding: 0;
    height: 100%;
}

.admin-header .navbar-brand {
    font-weight: bold;
    font-size: 1.3rem;
    color: white !important;
    padding: 0 20px;
    height: 100%;
    display: flex;
    align-items: center;
    border-left: 1px solid rgba(255,255,255,0.1);
}

.admin-header .navbar-nav .nav-link {
    color: rgba(255,255,255,0.9) !important;
    padding: 0 15px;
    height: var(--admin-header-height);
    display: flex;
    align-items: center;
    transition: var(--admin-transition);
}

.admin-header .navbar-nav .nav-link:hover {
    background: rgba(255,255,255,0.1);
    color: white !important;
}

.admin-header .dropdown-menu {
    border: none;
    box-shadow: var(--admin-box-shadow);
    border-radius: var(--admin-border-radius);
}

/* Sidebar الإدارة */
.admin-sidebar {
    width: var(--admin-sidebar-width);
    background: white;
    box-shadow: var(--admin-box-shadow);
    position: fixed;
    top: var(--admin-header-height);
    bottom: 0;
    right: 0;
    overflow-y: auto;
    z-index: 999;
    transition: var(--admin-transition);
}

.admin-sidebar.collapsed {
    width: 70px;
}

.admin-sidebar-menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.admin-sidebar-item {
    border-bottom: 1px solid #f1f1f1;
}

.admin-sidebar-link {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    color: #333;
    text-decoration: none;
    transition: var(--admin-transition);
    position: relative;
}

.admin-sidebar-link:hover {
    background: #f8f9fa;
    color: var(--admin-accent);
    text-decoration: none;
}

.admin-sidebar-link.active {
    background: var(--admin-accent);
    color: white;
}

.admin-sidebar-link.active::before {
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--admin-primary);
}

.admin-sidebar-icon {
    width: 20px;
    margin-left: 15px;
    text-align: center;
}

.admin-sidebar-text {
    flex: 1;
    transition: var(--admin-transition);
}

.admin-sidebar.collapsed .admin-sidebar-text {
    opacity: 0;
    width: 0;
}

.admin-sidebar-badge {
    font-size: 0.75rem;
    padding: 2px 6px;
    border-radius: 10px;
}

/* المحتوى الرئيسي */
.admin-content {
    flex: 1;
    margin-right: var(--admin-sidebar-width);
    padding: 30px;
    transition: var(--admin-transition);
}

.admin-sidebar.collapsed + .admin-content {
    margin-right: 70px;
}

/* عناوين الصفحات */
.admin-page-title {
    font-size: 2rem;
    font-weight: 600;
    color: var(--admin-primary);
    margin-bottom: 5px;
}

.admin-page-subtitle {
    color: #6c757d;
    margin-bottom: 0;
}

.admin-page-actions {
    display: flex;
    gap: 10px;
}

/* بطاقات الإحصائيات */
.stats-card {
    background: white;
    border-radius: var(--admin-border-radius);
    box-shadow: var(--admin-box-shadow);
    overflow: hidden;
    transition: var(--admin-transition);
    border: none;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.stats-card-body {
    padding: 25px;
    display: flex;
    align-items: center;
}

.stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    margin-left: 20px;
}

.stats-primary .stats-icon {
    background: linear-gradient(135deg, #007bff, #0056b3);
}

.stats-success .stats-icon {
    background: linear-gradient(135deg, #28a745, #1e7e34);
}

.stats-info .stats-icon {
    background: linear-gradient(135deg, #17a2b8, #117a8b);
}

.stats-warning .stats-icon {
    background: linear-gradient(135deg, #ffc107, #e0a800);
}

.stats-content {
    flex: 1;
}

.stats-number {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 5px;
    color: var(--admin-primary);
}

.stats-label {
    color: #6c757d;
    margin-bottom: 10px;
    font-size: 0.9rem;
}

.stats-details {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.stats-footer {
    padding: 15px 25px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

.stats-link {
    color: var(--admin-accent);
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    transition: var(--admin-transition);
}

.stats-link:hover {
    color: var(--admin-primary);
    text-decoration: none;
}

/* البطاقات العامة */
.card {
    border: none;
    border-radius: var(--admin-border-radius);
    box-shadow: var(--admin-box-shadow);
    margin-bottom: 20px;
}

.card-header {
    background: white;
    border-bottom: 1px solid #e9ecef;
    padding: 20px 25px;
    font-weight: 600;
}

.card-body {
    padding: 25px;
}

.card-footer {
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    padding: 15px 25px;
}

/* الجداول */
.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: var(--admin-primary);
    background: #f8f9fa;
}

.table-responsive {
    border-radius: var(--admin-border-radius);
}

/* الأزرار */
.btn {
    border-radius: var(--admin-border-radius);
    font-weight: 500;
    transition: var(--admin-transition);
    border: none;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn-primary {
    background: linear-gradient(135deg, var(--admin-accent), #2980b9);
}

.btn-success {
    background: linear-gradient(135deg, var(--admin-success), #229954);
}

.btn-warning {
    background: linear-gradient(135deg, var(--admin-warning), #d68910);
}

.btn-danger {
    background: linear-gradient(135deg, var(--admin-danger), #c0392b);
}

/* النماذج */
.form-control,
.form-select {
    border-radius: var(--admin-border-radius);
    border: 2px solid #e9ecef;
    transition: var(--admin-transition);
}

.form-control:focus,
.form-select:focus {
    border-color: var(--admin-accent);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

/* التنبيهات */
.alert {
    border-radius: var(--admin-border-radius);
    border: none;
    box-shadow: var(--admin-box-shadow);
}

/* قوائم المجموعات */
.list-group-item {
    border: none;
    border-bottom: 1px solid #f1f1f1;
    padding: 15px 20px;
}

.list-group-item:last-child {
    border-bottom: none;
}

/* شارات */
.badge {
    font-size: 0.75rem;
    padding: 4px 8px;
    border-radius: 12px;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .admin-sidebar {
        transform: translateX(100%);
        width: 100%;
    }
    
    .admin-sidebar.show {
        transform: translateX(0);
    }
    
    .admin-content {
        margin-right: 0;
        padding: 20px 15px;
    }
    
    .admin-page-title {
        font-size: 1.5rem;
    }
    
    .stats-card-body {
        padding: 20px;
    }
    
    .stats-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
        margin-left: 15px;
    }
    
    .stats-number {
        font-size: 1.5rem;
    }
    
    .card-body {
        padding: 20px;
    }
}

@media (max-width: 576px) {
    .admin-content {
        padding: 15px 10px;
    }
    
    .stats-card-body {
        flex-direction: column;
        text-align: center;
    }
    
    .stats-icon {
        margin-left: 0;
        margin-bottom: 15px;
    }
    
    .admin-page-actions {
        flex-direction: column;
        width: 100%;
    }
    
    .admin-page-actions .btn {
        width: 100%;
        margin-bottom: 5px;
    }
}

/* تحسينات إضافية */
.admin-loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.admin-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.admin-status-online {
    color: var(--admin-success);
}

.admin-status-offline {
    color: #6c757d;
}

.admin-notification-dot {
    position: absolute;
    top: 8px;
    left: 8px;
    width: 8px;
    height: 8px;
    background: var(--admin-danger);
    border-radius: 50%;
    border: 2px solid white;
}

/* تحسين الطباعة */
@media print {
    .admin-header,
    .admin-sidebar,
    .admin-page-actions {
        display: none !important;
    }
    
    .admin-content {
        margin-right: 0 !important;
        padding: 0 !important;
    }
    
    .stats-card,
    .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
}

/* تحسين إمكانية الوصول */
.admin-sidebar-link:focus {
    outline: 2px solid var(--admin-accent);
    outline-offset: -2px;
}

.btn:focus {
    outline: 2px solid var(--admin-accent);
    outline-offset: 2px;
}

/* تحسينات للوضع المظلم (اختياري) */
@media (prefers-color-scheme: dark) {
    .admin-body {
        background-color: #1a1a1a;
        color: #e9ecef;
    }
    
    .admin-sidebar,
    .card {
        background: #2d3748;
        color: #e9ecef;
    }
    
    .admin-sidebar-link {
        color: #e9ecef;
    }
    
    .admin-sidebar-link:hover {
        background: #4a5568;
    }
    
    .table th {
        background: #4a5568;
        color: #e9ecef;
    }
    
    .form-control,
    .form-select {
        background: #4a5568;
        border-color: #718096;
        color: #e9ecef;
    }
}
