/* 
 * ملف التصميم الرئيسي لموقع الإعلانات
 * Main CSS File for Classified Ads Website
 */

/* إعدادات عامة */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-radius: 8px;
    --box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    --transition: all 0.3s ease;
}

* {
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

/* Header Styles */
.header {
    box-shadow: var(--box-shadow);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: var(--transition);
}

.navbar-nav .nav-link:hover {
    color: #fff !important;
    transform: translateY(-1px);
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    min-height: 400px;
    display: flex;
    align-items: center;
}

.hero h1 {
    color: white;
}

.search-form .input-group {
    box-shadow: var(--box-shadow);
    border-radius: var(--border-radius);
    overflow: hidden;
}

.search-form .form-control,
.search-form .form-select {
    border: none;
    padding: 15px;
}

.search-form .btn {
    padding: 15px 20px;
    border: none;
}

.hero-stats .stat-card {
    background: rgba(255, 255, 255, 0.1);
    padding: 20px;
    border-radius: var(--border-radius);
    margin-bottom: 20px;
    backdrop-filter: blur(10px);
    transition: var(--transition);
}

.hero-stats .stat-card:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.2);
}

.hero-stats h3 {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 5px;
}

/* Categories Section */
.categories {
    background: white;
}

.category-card {
    transition: var(--transition);
    display: block;
}

.category-card:hover {
    transform: translateY(-5px);
    text-decoration: none;
}

.category-card .card {
    border: none;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.category-card:hover .card {
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.category-card .card-body {
    padding: 30px 20px;
}

.category-card i {
    transition: var(--transition);
}

.category-card:hover i {
    transform: scale(1.1);
    color: var(--primary-color) !important;
}

/* Ad Card Styles */
.ad-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.ad-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.ad-card-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.ad-card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.ad-card:hover .ad-card-image img {
    transform: scale(1.05);
}

.ad-card-badges {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 2;
}

.ad-card-badges .badge {
    margin-left: 5px;
    font-size: 0.7rem;
}

.ad-card-favorite {
    position: absolute;
    top: 10px;
    left: 10px;
    z-index: 2;
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.ad-card-favorite:hover {
    background: white;
    transform: scale(1.1);
}

.ad-card-favorite.active {
    color: var(--danger-color);
}

.ad-card-body {
    padding: 20px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.ad-card-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: #333;
    text-decoration: none;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.ad-card-title:hover {
    color: var(--primary-color);
}

.ad-card-description {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 15px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    flex-grow: 1;
}

.ad-card-price {
    font-size: 1.2rem;
    font-weight: bold;
    color: var(--success-color);
    margin-bottom: 10px;
}

.ad-card-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
    color: #999;
    margin-top: auto;
}

.ad-card-location {
    display: flex;
    align-items: center;
}

.ad-card-location i {
    margin-left: 5px;
}

.ad-card-date {
    display: flex;
    align-items: center;
}

.ad-card-date i {
    margin-left: 5px;
}

/* Featured Ads Section */
.featured-ads {
    background: linear-gradient(45deg, #f8f9fa, #e9ecef);
}

/* Buttons */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
    border: none;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn-primary {
    background: linear-gradient(45deg, var(--primary-color), #0056b3);
}

.btn-primary:hover {
    background: linear-gradient(45deg, #0056b3, var(--primary-color));
}

.btn-warning {
    background: linear-gradient(45deg, var(--warning-color), #e0a800);
    color: #333;
}

.btn-warning:hover {
    background: linear-gradient(45deg, #e0a800, var(--warning-color));
    color: #333;
}

/* Forms */
.form-control,
.form-select {
    border-radius: var(--border-radius);
    border: 2px solid #e9ecef;
    transition: var(--transition);
}

.form-control:focus,
.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Alerts */
.alert {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: var(--box-shadow);
}

/* Pagination */
.pagination .page-link {
    border-radius: var(--border-radius);
    margin: 0 2px;
    border: none;
    color: var(--primary-color);
}

.pagination .page-item.active .page-link {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

/* Footer */
.footer {
    background: linear-gradient(135deg, #2c3e50, #34495e) !important;
    margin-top: auto;
}

.footer h5,
.footer h6 {
    color: #fff;
    margin-bottom: 20px;
}

.footer a {
    color: #bdc3c7;
    text-decoration: none;
    transition: var(--transition);
}

.footer a:hover {
    color: #fff;
    transform: translateX(5px);
}

.social-links a {
    display: inline-block;
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    text-align: center;
    line-height: 40px;
    transition: var(--transition);
}

.social-links a:hover {
    background: var(--primary-color);
    transform: translateY(-3px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero {
        text-align: center;
        padding: 40px 0;
    }
    
    .hero h1 {
        font-size: 2rem;
    }
    
    .search-form .input-group {
        flex-direction: column;
    }
    
    .search-form .form-control,
    .search-form .form-select,
    .search-form .btn {
        border-radius: var(--border-radius);
        margin-bottom: 10px;
    }
    
    .hero-stats .stat-card {
        margin-bottom: 15px;
    }
    
    .ad-card-image {
        height: 150px;
    }
    
    .category-card .card-body {
        padding: 20px 15px;
    }
}

@media (max-width: 576px) {
    .hero h1 {
        font-size: 1.5rem;
    }
    
    .hero-stats h3 {
        font-size: 1.5rem;
    }
    
    .ad-card-body {
        padding: 15px;
    }
    
    .ad-card-title {
        font-size: 1rem;
    }
    
    .ad-card-price {
        font-size: 1.1rem;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Utilities */
.text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.text-truncate-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.shadow-hover {
    transition: var(--transition);
}

.shadow-hover:hover {
    box-shadow: var(--box-shadow);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #0056b3;
}
