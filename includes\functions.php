<?php
/**
 * ملف الوظائف المساعدة
 * Helper Functions for Classified Ads Website
 */

require_once 'config.php';
require_once 'database.php';

/**
 * وظائف إدارة المستخدمين
 */

// دالة لتسجيل الدخول
function loginUser($email, $password, $remember = false) {
    $db = Database::getInstance();
    
    // البحث عن المستخدم
    $user = $db->query("SELECT * FROM users WHERE email = ? AND status = 'active'", [$email])->fetch();
    
    if ($user && verifyPassword($password, $user['password'])) {
        // تحديث آخر تسجيل دخول
        $db->query("UPDATE users SET last_login = NOW() WHERE id = ?", [$user['id']]);
        
        // حفظ بيانات المستخدم في الجلسة
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['user_email'] = $user['email'];
        $_SESSION['user_name'] = $user['full_name'];
        $_SESSION['user_type'] = $user['user_type'];
        $_SESSION['logged_in'] = true;
        
        // إذا كان المستخدم يريد تذكر تسجيل الدخول
        if ($remember) {
            $token = bin2hex(random_bytes(32));
            setcookie('remember_token', $token, time() + (86400 * 30), '/'); // 30 يوم
            // يمكن حفظ الرمز في قاعدة البيانات للتحقق لاحقاً
        }
        
        return true;
    }
    
    return false;
}

// دالة لتسجيل الخروج
function logoutUser() {
    session_destroy();
    setcookie('remember_token', '', time() - 3600, '/');
    return true;
}

// دالة للتحقق من تسجيل الدخول
function isLoggedIn() {
    return isset($_SESSION['logged_in']) && $_SESSION['logged_in'] === true;
}

// دالة للحصول على المستخدم الحالي
function getCurrentUser() {
    if (!isLoggedIn()) {
        return null;
    }
    
    $db = Database::getInstance();
    return $db->find('users', $_SESSION['user_id']);
}

// دالة لتسجيل مستخدم جديد
function registerUser($data) {
    $db = Database::getInstance();
    
    // التحقق من عدم وجود البريد الإلكتروني
    if ($db->exists('users', ['email = ?'], [$data['email']])) {
        return ['success' => false, 'message' => 'البريد الإلكتروني مستخدم بالفعل'];
    }
    
    // التحقق من عدم وجود اسم المستخدم
    if ($db->exists('users', ['username = ?'], [$data['username']])) {
        return ['success' => false, 'message' => 'اسم المستخدم مستخدم بالفعل'];
    }
    
    // تشفير كلمة المرور
    $data['password'] = hashPassword($data['password']);
    $data['verification_token'] = bin2hex(random_bytes(32));
    
    try {
        $userId = $db->insert('users', $data)->lastInsertId();
        
        // إرسال بريد التفعيل (يمكن تنفيذه لاحقاً)
        // sendVerificationEmail($data['email'], $data['verification_token']);
        
        return ['success' => true, 'user_id' => $userId];
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'حدث خطأ أثناء التسجيل'];
    }
}

/**
 * وظائف إدارة الإعلانات
 */

// دالة للحصول على الإعلانات مع الترقيم
function getAds($filters = [], $page = 1, $perPage = null) {
    if ($perPage === null) {
        $perPage = ADS_PER_PAGE;
    }
    
    $db = Database::getInstance();
    $conditions = ["a.status = 'active'"];
    $params = [];
    
    // فلترة حسب القسم
    if (!empty($filters['category_id'])) {
        $conditions[] = "a.category_id = ?";
        $params[] = $filters['category_id'];
    }
    
    // فلترة حسب الموقع
    if (!empty($filters['location'])) {
        $conditions[] = "a.location LIKE ?";
        $params[] = '%' . $filters['location'] . '%';
    }
    
    // فلترة حسب السعر
    if (!empty($filters['min_price'])) {
        $conditions[] = "a.price >= ?";
        $params[] = $filters['min_price'];
    }
    
    if (!empty($filters['max_price'])) {
        $conditions[] = "a.price <= ?";
        $params[] = $filters['max_price'];
    }
    
    // البحث النصي
    if (!empty($filters['search'])) {
        $conditions[] = "(a.title LIKE ? OR a.description LIKE ?)";
        $params[] = '%' . $filters['search'] . '%';
        $params[] = '%' . $filters['search'] . '%';
    }
    
    // الترتيب
    $orderBy = "a.featured DESC, a.created_at DESC";
    if (!empty($filters['sort'])) {
        switch ($filters['sort']) {
            case 'price_low':
                $orderBy = "a.price ASC";
                break;
            case 'price_high':
                $orderBy = "a.price DESC";
                break;
            case 'newest':
                $orderBy = "a.created_at DESC";
                break;
            case 'oldest':
                $orderBy = "a.created_at ASC";
                break;
        }
    }
    
    $sql = "
        SELECT a.*, c.name_ar as category_name, u.full_name as user_name
        FROM ads a
        LEFT JOIN categories c ON a.category_id = c.id
        LEFT JOIN users u ON a.user_id = u.id
        WHERE " . implode(' AND ', $conditions) . "
        ORDER BY $orderBy
    ";
    
    return $db->paginate($sql, $params, $page, $perPage);
}

// دالة للحصول على إعلان واحد
function getAd($id) {
    $db = Database::getInstance();
    
    $sql = "
        SELECT a.*, c.name_ar as category_name, u.full_name as user_name, u.phone as user_phone
        FROM ads a
        LEFT JOIN categories c ON a.category_id = c.id
        LEFT JOIN users u ON a.user_id = u.id
        WHERE a.id = ? AND a.status = 'active'
    ";
    
    $ad = $db->query($sql, [$id])->fetch();
    
    if ($ad) {
        // زيادة عدد المشاهدات
        $db->query("UPDATE ads SET views = views + 1 WHERE id = ?", [$id]);
        
        // تسجيل الإحصائية
        recordStatistic($id, 'view');
        
        // الحصول على الصور
        $images = $db->query("SELECT * FROM ad_images WHERE ad_id = ? ORDER BY sort_order", [$id])->fetchAll();
        $ad['images'] = $images;
    }
    
    return $ad;
}

// دالة لإضافة إعلان جديد
function createAd($data, $images = []) {
    $db = Database::getInstance();
    
    try {
        $db->beginTransaction();
        
        // إدراج الإعلان
        $adId = $db->insert('ads', $data)->lastInsertId();
        
        // رفع الصور
        if (!empty($images)) {
            foreach ($images as $index => $image) {
                $imagePath = uploadImage($image, 'ads');
                if ($imagePath) {
                    $imageData = [
                        'ad_id' => $adId,
                        'image_path' => $imagePath,
                        'image_name' => $image['name'],
                        'is_primary' => $index === 0 ? 1 : 0,
                        'sort_order' => $index
                    ];
                    $db->insert('ad_images', $imageData);
                }
            }
        }
        
        $db->commit();
        return ['success' => true, 'ad_id' => $adId];
        
    } catch (Exception $e) {
        $db->rollback();
        return ['success' => false, 'message' => 'حدث خطأ أثناء إضافة الإعلان'];
    }
}

/**
 * وظائف إدارة الأقسام
 */

// دالة للحصول على جميع الأقسام
function getCategories($parentId = null, $status = 'active') {
    $db = Database::getInstance();
    
    $conditions = ["status = ?"];
    $params = [$status];
    
    if ($parentId === null) {
        $conditions[] = "parent_id IS NULL";
    } else {
        $conditions[] = "parent_id = ?";
        $params[] = $parentId;
    }
    
    return $db->findAll('categories', $conditions, $params, 'sort_order ASC, name_ar ASC');
}

// دالة للحصول على شجرة الأقسام
function getCategoryTree() {
    $categories = getCategories();
    $tree = [];
    
    foreach ($categories as $category) {
        $category['children'] = getCategories($category['id']);
        $tree[] = $category;
    }
    
    return $tree;
}

/**
 * وظائف رفع الملفات
 */

// دالة لرفع صورة
function uploadImage($file, $folder = 'general') {
    if (!isset($file['tmp_name']) || empty($file['tmp_name'])) {
        return false;
    }
    
    // التحقق من نوع الملف
    $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if (!in_array($fileExtension, ALLOWED_IMAGE_TYPES)) {
        return false;
    }
    
    // التحقق من حجم الملف
    if ($file['size'] > MAX_FILE_SIZE) {
        return false;
    }
    
    // إنشاء اسم فريد للملف
    $fileName = uniqid() . '_' . time() . '.' . $fileExtension;
    $uploadPath = UPLOAD_PATH . $folder . '/';
    
    // إنشاء المجلد إذا لم يكن موجوداً
    if (!is_dir($uploadPath)) {
        mkdir($uploadPath, 0755, true);
    }
    
    $fullPath = $uploadPath . $fileName;
    
    // رفع الملف
    if (move_uploaded_file($file['tmp_name'], $fullPath)) {
        // تحسين الصورة (اختياري)
        optimizeImage($fullPath);
        
        return $folder . '/' . $fileName;
    }
    
    return false;
}

// دالة لتحسين الصورة
function optimizeImage($imagePath, $quality = 80) {
    $imageInfo = getimagesize($imagePath);
    if (!$imageInfo) return false;
    
    $imageType = $imageInfo[2];
    
    switch ($imageType) {
        case IMAGETYPE_JPEG:
            $image = imagecreatefromjpeg($imagePath);
            imagejpeg($image, $imagePath, $quality);
            break;
        case IMAGETYPE_PNG:
            $image = imagecreatefrompng($imagePath);
            imagepng($image, $imagePath, 9);
            break;
        case IMAGETYPE_GIF:
            $image = imagecreatefromgif($imagePath);
            imagegif($image, $imagePath);
            break;
    }
    
    if (isset($image)) {
        imagedestroy($image);
        return true;
    }
    
    return false;
}

/**
 * وظائف الإحصائيات
 */

// دالة لتسجيل إحصائية
function recordStatistic($adId, $actionType, $userId = null, $searchQuery = null) {
    $db = Database::getInstance();
    
    $data = [
        'ad_id' => $adId,
        'user_id' => $userId,
        'action_type' => $actionType,
        'ip_address' => getRealIP(),
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
        'referrer' => $_SERVER['HTTP_REFERER'] ?? '',
        'search_query' => $searchQuery
    ];
    
    try {
        $db->insert('statistics', $data);
    } catch (Exception $e) {
        // تجاهل أخطاء الإحصائيات
    }
}

/**
 * وظائف التحقق من الصلاحيات
 */

// دالة للتحقق من ملكية الإعلان
function canEditAd($adId, $userId = null) {
    if ($userId === null && !isLoggedIn()) {
        return false;
    }
    
    $userId = $userId ?? $_SESSION['user_id'];
    $db = Database::getInstance();
    
    return $db->exists('ads', ['id = ? AND user_id = ?'], [$adId, $userId]);
}

/**
 * وظائف البريد الإلكتروني
 */

// دالة لإرسال بريد إلكتروني
function sendEmail($to, $subject, $body, $isHTML = true) {
    // يمكن استخدام PHPMailer أو أي مكتبة أخرى
    // هذا مثال بسيط باستخدام mail() function
    
    $headers = "From: " . ADMIN_EMAIL . "\r\n";
    $headers .= "Reply-To: " . ADMIN_EMAIL . "\r\n";
    
    if ($isHTML) {
        $headers .= "Content-Type: text/html; charset=UTF-8\r\n";
    } else {
        $headers .= "Content-Type: text/plain; charset=UTF-8\r\n";
    }
    
    return mail($to, $subject, $body, $headers);
}

/**
 * وظائف التنظيف والتحقق
 */

// دالة للتحقق من صحة رقم الهاتف
function isValidPhone($phone) {
    return preg_match('/^[0-9+\-\s()]{10,15}$/', $phone);
}

// دالة للتحقق من صحة السعر
function isValidPrice($price) {
    return is_numeric($price) && $price >= 0;
}

// دالة لتنظيف النص من HTML
function stripHtml($text) {
    return strip_tags($text);
}

// دالة لاقتطاع النص
function truncateText($text, $length = 100, $suffix = '...') {
    if (mb_strlen($text) <= $length) {
        return $text;
    }
    
    return mb_substr($text, 0, $length) . $suffix;
}

/**
 * وظائف مساعدة للعرض
 */

// دالة لعرض رسائل التنبيه
function showAlert($message, $type = 'info') {
    $alertClass = [
        'success' => 'alert-success',
        'error' => 'alert-danger',
        'warning' => 'alert-warning',
        'info' => 'alert-info'
    ];
    
    $class = $alertClass[$type] ?? 'alert-info';
    
    return "<div class='alert $class alert-dismissible fade show' role='alert'>
                $message
                <button type='button' class='btn-close' data-bs-dismiss='alert'></button>
            </div>";
}

// دالة لعرض رسائل الجلسة
function showSessionMessages() {
    $output = '';
    
    if (isset($_SESSION['success'])) {
        $output .= showAlert($_SESSION['success'], 'success');
        unset($_SESSION['success']);
    }
    
    if (isset($_SESSION['error'])) {
        $output .= showAlert($_SESSION['error'], 'error');
        unset($_SESSION['error']);
    }
    
    if (isset($_SESSION['warning'])) {
        $output .= showAlert($_SESSION['warning'], 'warning');
        unset($_SESSION['warning']);
    }
    
    if (isset($_SESSION['info'])) {
        $output .= showAlert($_SESSION['info'], 'info');
        unset($_SESSION['info']);
    }
    
    return $output;
}

?>
