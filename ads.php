<?php
/**
 * صفحة عرض جميع الإعلانات
 * All Ads Page
 */

require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'includes/models.php';

// معالجة المرشحات
$filters = [
    'search' => $_GET['q'] ?? '',
    'category_id' => $_GET['category'] ?? '',
    'location' => $_GET['location'] ?? '',
    'min_price' => $_GET['min_price'] ?? '',
    'max_price' => $_GET['max_price'] ?? '',
    'sort' => $_GET['sort'] ?? 'newest',
    'featured' => $_GET['featured'] ?? ''
];

// إزالة القيم الفارغة
$filters = array_filter($filters, function($value) {
    return $value !== '';
});

// الصفحة الحالية
$currentPage = max(1, intval($_GET['page'] ?? 1));

// الحصول على الإعلانات
$adsResult = getAds($filters, $currentPage);
$ads = $adsResult['data'];
$pagination = $adsResult;

// الحصول على الأقسام للفلترة
$categories = getCategoryTree();

// الحصول على المواقع الشائعة
$db = Database::getInstance();
$popularLocations = $db->query("
    SELECT location, COUNT(*) as count 
    FROM ads 
    WHERE status = 'active' AND location != '' 
    GROUP BY location 
    ORDER BY count DESC 
    LIMIT 10
")->fetchAll();

$pageTitle = 'جميع الإعلانات';
if (!empty($filters['search'])) {
    $pageTitle = 'نتائج البحث عن: ' . htmlspecialchars($filters['search']);
} elseif (!empty($filters['category_id'])) {
    $category = $db->find('categories', $filters['category_id']);
    if ($category) {
        $pageTitle = 'إعلانات ' . $category['name_ar'];
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - <?php echo getSetting('site_name'); ?></title>
    <meta name="description" content="تصفح جميع الإعلانات المبوبة">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="<?php echo asset('css/style.css'); ?>" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <?php include 'includes/header.php'; ?>

    <div class="container my-4">
        <div class="row">
            <!-- Sidebar Filters -->
            <div class="col-lg-3 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-filter me-2"></i>
                            تصفية النتائج
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="GET" action="<?php echo url('ads.php'); ?>" id="filterForm">
                            <!-- البحث النصي -->
                            <div class="mb-3">
                                <label class="form-label">البحث</label>
                                <input type="text" class="form-control" name="q" 
                                       value="<?php echo htmlspecialchars($filters['search'] ?? ''); ?>" 
                                       placeholder="ابحث في الإعلانات...">
                            </div>
                            
                            <!-- القسم -->
                            <div class="mb-3">
                                <label class="form-label">القسم</label>
                                <select class="form-select" name="category">
                                    <option value="">جميع الأقسام</option>
                                    <?php foreach ($categories as $category): ?>
                                        <option value="<?php echo $category['id']; ?>" 
                                                <?php echo ($filters['category_id'] ?? '') == $category['id'] ? 'selected' : ''; ?>>
                                            <?php echo $category['name_ar']; ?>
                                        </option>
                                        <?php if (!empty($category['children'])): ?>
                                            <?php foreach ($category['children'] as $child): ?>
                                                <option value="<?php echo $child['id']; ?>" 
                                                        <?php echo ($filters['category_id'] ?? '') == $child['id'] ? 'selected' : ''; ?>>
                                                    &nbsp;&nbsp;- <?php echo $child['name_ar']; ?>
                                                </option>
                                            <?php endforeach; ?>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <!-- الموقع -->
                            <div class="mb-3">
                                <label class="form-label">الموقع</label>
                                <input type="text" class="form-control" name="location" 
                                       value="<?php echo htmlspecialchars($filters['location'] ?? ''); ?>" 
                                       placeholder="المدينة أو المنطقة"
                                       list="locationsList">
                                <datalist id="locationsList">
                                    <?php foreach ($popularLocations as $location): ?>
                                        <option value="<?php echo htmlspecialchars($location['location']); ?>">
                                    <?php endforeach; ?>
                                </datalist>
                            </div>
                            
                            <!-- نطاق السعر -->
                            <div class="mb-3">
                                <label class="form-label">نطاق السعر</label>
                                <div class="row">
                                    <div class="col-6">
                                        <input type="number" class="form-control" name="min_price" 
                                               value="<?php echo htmlspecialchars($filters['min_price'] ?? ''); ?>" 
                                               placeholder="من" min="0">
                                    </div>
                                    <div class="col-6">
                                        <input type="number" class="form-control" name="max_price" 
                                               value="<?php echo htmlspecialchars($filters['max_price'] ?? ''); ?>" 
                                               placeholder="إلى" min="0">
                                    </div>
                                </div>
                            </div>
                            
                            <!-- الترتيب -->
                            <div class="mb-3">
                                <label class="form-label">ترتيب حسب</label>
                                <select class="form-select" name="sort">
                                    <option value="newest" <?php echo ($filters['sort'] ?? '') == 'newest' ? 'selected' : ''; ?>>
                                        الأحدث أولاً
                                    </option>
                                    <option value="oldest" <?php echo ($filters['sort'] ?? '') == 'oldest' ? 'selected' : ''; ?>>
                                        الأقدم أولاً
                                    </option>
                                    <option value="price_low" <?php echo ($filters['sort'] ?? '') == 'price_low' ? 'selected' : ''; ?>>
                                        السعر من الأقل للأعلى
                                    </option>
                                    <option value="price_high" <?php echo ($filters['sort'] ?? '') == 'price_high' ? 'selected' : ''; ?>>
                                        السعر من الأعلى للأقل
                                    </option>
                                </select>
                            </div>
                            
                            <!-- خيارات إضافية -->
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="featured" value="1" 
                                           <?php echo !empty($filters['featured']) ? 'checked' : ''; ?>>
                                    <label class="form-check-label">
                                        الإعلانات المميزة فقط
                                    </label>
                                </div>
                            </div>
                            
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-2"></i>
                                    تطبيق الفلاتر
                                </button>
                                <a href="<?php echo url('ads.php'); ?>" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-2"></i>
                                    مسح الفلاتر
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- إحصائيات سريعة -->
                <div class="card mt-3">
                    <div class="card-body text-center">
                        <h6 class="card-title">إحصائيات البحث</h6>
                        <p class="mb-1">
                            <strong><?php echo formatNumber($pagination['total']); ?></strong> إعلان
                        </p>
                        <small class="text-muted">
                            الصفحة <?php echo $pagination['page']; ?> من <?php echo $pagination['total_pages']; ?>
                        </small>
                    </div>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-lg-9">
                <!-- Header with results info -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h2><?php echo $pageTitle; ?></h2>
                        <p class="text-muted mb-0">
                            عرض <?php echo formatNumber(count($ads)); ?> من أصل <?php echo formatNumber($pagination['total']); ?> إعلان
                        </p>
                    </div>
                    
                    <!-- View Toggle -->
                    <div class="btn-group" role="group">
                        <input type="radio" class="btn-check" name="viewType" id="gridView" checked>
                        <label class="btn btn-outline-primary" for="gridView">
                            <i class="fas fa-th"></i>
                        </label>
                        
                        <input type="radio" class="btn-check" name="viewType" id="listView">
                        <label class="btn btn-outline-primary" for="listView">
                            <i class="fas fa-list"></i>
                        </label>
                    </div>
                </div>
                
                <!-- Active Filters -->
                <?php if (!empty(array_filter($filters))): ?>
                <div class="active-filters mb-3">
                    <h6>الفلاتر النشطة:</h6>
                    <div class="d-flex flex-wrap gap-2">
                        <?php if (!empty($filters['search'])): ?>
                            <span class="badge bg-primary">
                                البحث: <?php echo htmlspecialchars($filters['search']); ?>
                                <a href="<?php echo url('ads.php?' . http_build_query(array_diff_key($filters, ['search' => '']))); ?>" 
                                   class="text-white ms-1">×</a>
                            </span>
                        <?php endif; ?>
                        
                        <?php if (!empty($filters['category_id'])): ?>
                            <?php 
                            $category = $db->find('categories', $filters['category_id']);
                            if ($category):
                            ?>
                            <span class="badge bg-info">
                                القسم: <?php echo $category['name_ar']; ?>
                                <a href="<?php echo url('ads.php?' . http_build_query(array_diff_key($filters, ['category_id' => '']))); ?>" 
                                   class="text-white ms-1">×</a>
                            </span>
                            <?php endif; ?>
                        <?php endif; ?>
                        
                        <?php if (!empty($filters['location'])): ?>
                            <span class="badge bg-success">
                                الموقع: <?php echo htmlspecialchars($filters['location']); ?>
                                <a href="<?php echo url('ads.php?' . http_build_query(array_diff_key($filters, ['location' => '']))); ?>" 
                                   class="text-white ms-1">×</a>
                            </span>
                        <?php endif; ?>
                        
                        <?php if (!empty($filters['min_price']) || !empty($filters['max_price'])): ?>
                            <span class="badge bg-warning text-dark">
                                السعر: 
                                <?php echo !empty($filters['min_price']) ? formatNumber($filters['min_price']) : '0'; ?> - 
                                <?php echo !empty($filters['max_price']) ? formatNumber($filters['max_price']) : '∞'; ?>
                                <a href="<?php echo url('ads.php?' . http_build_query(array_diff_key($filters, ['min_price' => '', 'max_price' => '']))); ?>" 
                                   class="text-dark ms-1">×</a>
                            </span>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>
                
                <!-- Ads Grid -->
                <?php if (!empty($ads)): ?>
                    <div class="ads-container" id="adsContainer">
                        <div class="row" id="adsGrid">
                            <?php foreach ($ads as $ad): ?>
                                <div class="col-lg-4 col-md-6 mb-4 ad-item">
                                    <?php include 'includes/ad-card.php'; ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    
                    <!-- Pagination -->
                    <?php if ($pagination['total_pages'] > 1): ?>
                        <nav aria-label="تصفح الصفحات" class="mt-4">
                            <ul class="pagination justify-content-center">
                                <?php if ($pagination['has_prev']): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="<?php echo url('ads.php?' . http_build_query(array_merge($filters, ['page' => $pagination['page'] - 1]))); ?>">
                                            <i class="fas fa-chevron-right"></i>
                                        </a>
                                    </li>
                                <?php endif; ?>
                                
                                <?php
                                $startPage = max(1, $pagination['page'] - 2);
                                $endPage = min($pagination['total_pages'], $pagination['page'] + 2);
                                
                                for ($i = $startPage; $i <= $endPage; $i++):
                                ?>
                                    <li class="page-item <?php echo $i == $pagination['page'] ? 'active' : ''; ?>">
                                        <a class="page-link" href="<?php echo url('ads.php?' . http_build_query(array_merge($filters, ['page' => $i]))); ?>">
                                            <?php echo $i; ?>
                                        </a>
                                    </li>
                                <?php endfor; ?>
                                
                                <?php if ($pagination['has_next']): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="<?php echo url('ads.php?' . http_build_query(array_merge($filters, ['page' => $pagination['page'] + 1]))); ?>">
                                            <i class="fas fa-chevron-left"></i>
                                        </a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                    <?php endif; ?>
                    
                <?php else: ?>
                    <!-- No Results -->
                    <div class="text-center py-5">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h4>لا توجد إعلانات</h4>
                        <p class="text-muted">لم يتم العثور على إعلانات تطابق معايير البحث الخاصة بك.</p>
                        <a href="<?php echo url('ads.php'); ?>" class="btn btn-primary">
                            عرض جميع الإعلانات
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <?php include 'includes/footer.php'; ?>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="<?php echo asset('js/main.js'); ?>"></script>
    
    <script>
    // تبديل عرض الشبكة/القائمة
    document.addEventListener('DOMContentLoaded', function() {
        const gridView = document.getElementById('gridView');
        const listView = document.getElementById('listView');
        const adsContainer = document.getElementById('adsContainer');
        
        listView.addEventListener('change', function() {
            if (this.checked) {
                adsContainer.classList.add('list-view');
                document.querySelectorAll('.ad-item').forEach(item => {
                    item.className = 'col-12 mb-3 ad-item';
                });
            }
        });
        
        gridView.addEventListener('change', function() {
            if (this.checked) {
                adsContainer.classList.remove('list-view');
                document.querySelectorAll('.ad-item').forEach(item => {
                    item.className = 'col-lg-4 col-md-6 mb-4 ad-item';
                });
            }
        });
        
        // تطبيق الفلاتر تلقائياً عند التغيير
        const filterForm = document.getElementById('filterForm');
        const autoSubmitElements = filterForm.querySelectorAll('select, input[type="checkbox"]');
        
        autoSubmitElements.forEach(element => {
            element.addEventListener('change', function() {
                filterForm.submit();
            });
        });
    });
    </script>
</body>
</html>
