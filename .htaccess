# إعدادات Apache لموقع الإعلانات المبوبة
# Apache Configuration for Classified Ads Website

# تفعيل إعادة الكتابة
RewriteEngine On

# منع عرض محتويات المجلدات
Options -Indexes

# حماية الملفات الحساسة
<Files "*.log">
    Order allow,deny
    Deny from all
</Files>

<Files "*.sql">
    Order allow,deny
    Deny from all
</Files>

<Files "*.bak">
    Order allow,deny
    Deny from all
</Files>

<Files "config.php">
    Order allow,deny
    Deny from all
</Files>

# إعادة توجيه HTTP إلى HTTPS (اختياري - قم بإلغاء التعليق عند الحاجة)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# إعادة توجيه www إلى non-www (اختياري)
# RewriteCond %{HTTP_HOST} ^www\.(.*)$ [NC]
# RewriteRule ^(.*)$ http://%1/$1 [R=301,L]

# URLs صديقة لمحركات البحث
RewriteRule ^ad/([0-9]+)/(.*)$ ad-details.php?id=$1 [L,QSA]
RewriteRule ^category/([0-9]+)/(.*)$ category.php?id=$1 [L,QSA]
RewriteRule ^user/([0-9]+)/(.*)$ user-profile.php?id=$1 [L,QSA]
RewriteRule ^search/(.*)$ search.php?q=$1 [L,QSA]
RewriteRule ^page/(.*)$ page.php?slug=$1 [L,QSA]

# حماية من الهجمات الشائعة
RewriteCond %{QUERY_STRING} (<|%3C).*script.*(>|%3E) [NC,OR]
RewriteCond %{QUERY_STRING} GLOBALS(=|\[|\%[0-9A-Z]{0,2}) [OR]
RewriteCond %{QUERY_STRING} _REQUEST(=|\[|\%[0-9A-Z]{0,2}) [OR]
RewriteCond %{QUERY_STRING} ^.*(\[|\]|\(|\)|<|>|ê|"|;|\?|\*|=$).* [NC,OR]
RewriteCond %{QUERY_STRING} ^.*("|'|<|>|\|{||).* [NC,OR]
RewriteCond %{QUERY_STRING} ^.*(%0|%A|%B|%C|%D|%E|%F|127\.0).* [NC,OR]
RewriteCond %{QUERY_STRING} ^.*(globals|encode|localhost|loopback).* [NC,OR]
RewriteCond %{QUERY_STRING} ^.*(request|select|insert|union|declare).* [NC]
RewriteRule ^(.*)$ - [F,L]

# منع الوصول من User Agents المشبوهة
RewriteCond %{HTTP_USER_AGENT} ^$ [OR]
RewriteCond %{HTTP_USER_AGENT} ^(-|curl|wget|libwww-perl|python|nikto|scan) [NC,OR]
RewriteCond %{HTTP_USER_AGENT} (<|>|'|%0A|%0D|%27|%3C|%3E|%00) [NC]
RewriteRule ^(.*)$ - [F,L]

# حماية من Hotlinking للصور
RewriteCond %{HTTP_REFERER} !^$
RewriteCond %{HTTP_REFERER} !^http(s)?://(www\.)?yourdomain.com [NC]
RewriteCond %{HTTP_REFERER} !^http(s)?://(www\.)?google.com [NC]
RewriteCond %{HTTP_REFERER} !^http(s)?://(www\.)?bing.com [NC]
RewriteRule \.(jpg|jpeg|png|gif)$ assets/images/hotlink-protection.jpg [R,L]

# إعدادات الأمان
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options SAMEORIGIN
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set Permissions-Policy "geolocation=(), microphone=(), camera=()"
    
    # إزالة معلومات الخادم
    Header unset Server
    Header unset X-Powered-By
</IfModule>

# إعدادات التخزين المؤقت
<IfModule mod_expires.c>
    ExpiresActive On
    
    # HTML
    ExpiresByType text/html "access plus 1 hour"
    
    # CSS و JavaScript
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    
    # الصور
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/ico "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType image/webp "access plus 1 year"
    
    # الخطوط
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    ExpiresByType application/font-woff2 "access plus 1 year"
    
    # ملفات أخرى
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType application/zip "access plus 1 month"
</IfModule>

# ضغط الملفات
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
    
    # استثناء الملفات المضغوطة مسبقاً
    SetEnvIfNoCase Request_URI \
        \.(?:gif|jpe?g|png|zip|gz|bz2|sit|rar)$ no-gzip dont-vary
</IfModule>

# إعدادات PHP
<IfModule mod_php.c>
    # حد أقصى لحجم الملف المرفوع
    php_value upload_max_filesize 10M
    php_value post_max_size 10M
    php_value max_execution_time 300
    php_value max_input_time 300
    php_value memory_limit 256M
    
    # إعدادات الجلسة
    php_value session.cookie_httponly 1
    php_value session.use_only_cookies 1
    php_value session.cookie_lifetime 0
    php_value session.gc_maxlifetime 3600
    
    # إعدادات الأمان
    php_flag expose_php Off
    php_flag display_errors Off
    php_flag log_errors On
    php_value error_log logs/error.log
    
    # تفعيل OPcache (إذا كان متاحاً)
    php_value opcache.enable 1
    php_value opcache.memory_consumption 128
    php_value opcache.max_accelerated_files 4000
    php_value opcache.revalidate_freq 60
</IfModule>

# تحديد أنواع MIME
<IfModule mod_mime.c>
    AddType application/javascript .js
    AddType text/css .css
    AddType image/svg+xml .svg
    AddType application/font-woff .woff
    AddType application/font-woff2 .woff2
    AddType image/webp .webp
</IfModule>

# إعدادات ETag
<IfModule mod_headers.c>
    Header unset ETag
</IfModule>
FileETag None

# حماية إضافية للمجلدات الحساسة
RedirectMatch 403 ^/includes/.*$
RedirectMatch 403 ^/logs/.*$
RedirectMatch 403 ^/sql/.*$

# صفحات الخطأ المخصصة
ErrorDocument 400 /errors/400.php
ErrorDocument 401 /errors/401.php
ErrorDocument 403 /errors/403.php
ErrorDocument 404 /errors/404.php
ErrorDocument 500 /errors/500.php
ErrorDocument 503 /errors/503.php

# منع الوصول للملفات المخفية
<Files ".*">
    Order allow,deny
    Deny from all
</Files>

# السماح بالوصول لملفات معينة
<Files ".well-known">
    Order allow,deny
    Allow from all
</Files>

# تحسين الأداء - تجميع الطلبات
<IfModule mod_rewrite.c>
    # تجميع ملفات CSS
    RewriteRule ^assets/css/combined\.css$ assets/css/combine.php [L]
    
    # تجميع ملفات JavaScript
    RewriteRule ^assets/js/combined\.js$ assets/js/combine.php [L]
</IfModule>

# حماية من Directory Traversal
RewriteCond %{THE_REQUEST} \s/+(.*/)?\.\.[\\/]
RewriteRule ^.*$ - [F,L]

# منع الوصول للملفات التنفيذية غير المرغوبة
<FilesMatch "\.(bat|exe|cmd|sh|cgi)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# تحسين الأمان - منع تنفيذ PHP في مجلد الرفع
<Directory "assets/uploads">
    <Files "*.php">
        Order allow,deny
        Deny from all
    </Files>
    <Files "*.phtml">
        Order allow,deny
        Deny from all
    </Files>
    <Files "*.php3">
        Order allow,deny
        Deny from all
    </Files>
    <Files "*.php4">
        Order allow,deny
        Deny from all
    </Files>
    <Files "*.php5">
        Order allow,deny
        Deny from all
    </Files>
</Directory>

# إعدادات CORS (إذا كنت تستخدم API)
<IfModule mod_headers.c>
    # السماح بالوصول من نطاقات محددة فقط
    # Header set Access-Control-Allow-Origin "https://yourdomain.com"
    # Header set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
    # Header set Access-Control-Allow-Headers "Content-Type, Authorization"
</IfModule>
