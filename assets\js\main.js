/**
 * ملف JavaScript الرئيسي لموقع الإعلانات
 * Main JavaScript File for Classified Ads Website
 */

// إعدادات عامة
const CONFIG = {
    baseUrl: window.location.origin + '/api',
    ajaxTimeout: 30000,
    imageMaxSize: 5 * 1024 * 1024, // 5MB
    allowedImageTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
};

// دالة لإظهار التنبيهات
function showAlert(message, type = 'info', duration = 5000) {
    const alertTypes = {
        'success': 'alert-success',
        'error': 'alert-danger',
        'warning': 'alert-warning',
        'info': 'alert-info'
    };
    
    const alertClass = alertTypes[type] || 'alert-info';
    const alertId = 'alert-' + Date.now();
    
    const alertHtml = `
        <div id="${alertId}" class="alert ${alertClass} alert-dismissible fade show position-fixed" 
             style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    document.body.insertAdjacentHTML('beforeend', alertHtml);
    
    // إزالة التنبيه تلقائياً
    setTimeout(() => {
        const alert = document.getElementById(alertId);
        if (alert) {
            alert.remove();
        }
    }, duration);
}

// دالة لإظهار/إخفاء مؤشر التحميل
function toggleLoading(element, show = true) {
    if (show) {
        element.disabled = true;
        const originalText = element.innerHTML;
        element.setAttribute('data-original-text', originalText);
        element.innerHTML = '<span class="loading"></span> جاري التحميل...';
    } else {
        element.disabled = false;
        const originalText = element.getAttribute('data-original-text');
        if (originalText) {
            element.innerHTML = originalText;
        }
    }
}

// دالة لإرسال طلبات AJAX
function sendAjaxRequest(url, data = {}, method = 'POST') {
    return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();
        xhr.timeout = CONFIG.ajaxTimeout;
        
        xhr.onreadystatechange = function() {
            if (xhr.readyState === 4) {
                if (xhr.status === 200) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        resolve(response);
                    } catch (e) {
                        reject(new Error('خطأ في تحليل الاستجابة'));
                    }
                } else {
                    reject(new Error('خطأ في الشبكة: ' + xhr.status));
                }
            }
        };
        
        xhr.ontimeout = function() {
            reject(new Error('انتهت مهلة الطلب'));
        };
        
        xhr.onerror = function() {
            reject(new Error('خطأ في الشبكة'));
        };
        
        if (method === 'GET') {
            const params = new URLSearchParams(data).toString();
            url += (url.includes('?') ? '&' : '?') + params;
            xhr.open('GET', url, true);
            xhr.send();
        } else {
            xhr.open(method, url, true);
            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
            
            const formData = new URLSearchParams();
            for (const key in data) {
                formData.append(key, data[key]);
            }
            
            xhr.send(formData.toString());
        }
    });
}

// إدارة المفضلة
class FavoriteManager {
    static toggle(adId, button) {
        const isActive = button.classList.contains('active');
        const action = isActive ? 'remove' : 'add';
        
        toggleLoading(button);
        
        sendAjaxRequest(`${CONFIG.baseUrl}/ajax/favorite.php`, {
            action: action,
            ad_id: adId
        })
        .then(response => {
            if (response.success) {
                if (action === 'add') {
                    button.classList.add('active');
                    button.innerHTML = '<i class="fas fa-heart"></i>';
                    showAlert('تم إضافة الإعلان للمفضلة', 'success');
                } else {
                    button.classList.remove('active');
                    button.innerHTML = '<i class="far fa-heart"></i>';
                    showAlert('تم إزالة الإعلان من المفضلة', 'info');
                }
            } else {
                showAlert(response.message || 'حدث خطأ', 'error');
            }
        })
        .catch(error => {
            showAlert(error.message, 'error');
        })
        .finally(() => {
            toggleLoading(button, false);
        });
    }
}

// إدارة البحث
class SearchManager {
    static init() {
        const searchForm = document.querySelector('.search-form');
        if (searchForm) {
            searchForm.addEventListener('submit', this.handleSearch.bind(this));
        }
        
        // البحث التلقائي
        const searchInput = document.querySelector('input[name="q"]');
        if (searchInput) {
            let searchTimeout;
            searchInput.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    this.autoSearch(e.target.value);
                }, 500);
            });
        }
    }
    
    static handleSearch(e) {
        e.preventDefault();
        const formData = new FormData(e.target);
        const searchParams = new URLSearchParams(formData);
        window.location.href = `${CONFIG.baseUrl}/search.php?${searchParams.toString()}`;
    }
    
    static autoSearch(query) {
        if (query.length < 3) return;
        
        sendAjaxRequest(`${CONFIG.baseUrl}/ajax/search-suggestions.php`, {
            q: query
        }, 'GET')
        .then(response => {
            if (response.success) {
                this.showSuggestions(response.suggestions);
            }
        })
        .catch(error => {
            console.error('خطأ في البحث التلقائي:', error);
        });
    }
    
    static showSuggestions(suggestions) {
        // إظهار اقتراحات البحث
        let suggestionsHtml = '';
        suggestions.forEach(suggestion => {
            suggestionsHtml += `
                <div class="suggestion-item" onclick="SearchManager.selectSuggestion('${suggestion}')">
                    ${suggestion}
                </div>
            `;
        });
        
        // يمكن إضافة عنصر لعرض الاقتراحات
        console.log('اقتراحات البحث:', suggestions);
    }
    
    static selectSuggestion(suggestion) {
        const searchInput = document.querySelector('input[name="q"]');
        if (searchInput) {
            searchInput.value = suggestion;
            searchInput.closest('form').submit();
        }
    }
}

// إدارة رفع الصور
class ImageUploader {
    static init() {
        const imageInputs = document.querySelectorAll('input[type="file"][accept*="image"]');
        imageInputs.forEach(input => {
            input.addEventListener('change', this.handleImageSelect.bind(this));
        });
    }
    
    static handleImageSelect(e) {
        const files = Array.from(e.target.files);
        const validFiles = [];
        
        files.forEach(file => {
            if (this.validateImage(file)) {
                validFiles.push(file);
            }
        });
        
        if (validFiles.length > 0) {
            this.previewImages(validFiles, e.target);
        }
    }
    
    static validateImage(file) {
        // التحقق من نوع الملف
        if (!CONFIG.allowedImageTypes.includes(file.type)) {
            showAlert('نوع الملف غير مدعوم. يرجى اختيار صورة بصيغة JPG, PNG, GIF أو WebP', 'error');
            return false;
        }
        
        // التحقق من حجم الملف
        if (file.size > CONFIG.imageMaxSize) {
            showAlert('حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت', 'error');
            return false;
        }
        
        return true;
    }
    
    static previewImages(files, input) {
        const previewContainer = input.parentElement.querySelector('.image-preview') || 
                               this.createPreviewContainer(input);
        
        files.forEach((file, index) => {
            const reader = new FileReader();
            reader.onload = (e) => {
                const imageHtml = `
                    <div class="preview-item" data-index="${index}">
                        <img src="${e.target.result}" alt="معاينة الصورة">
                        <button type="button" class="btn btn-sm btn-danger remove-image" 
                                onclick="ImageUploader.removePreview(this)">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `;
                previewContainer.insertAdjacentHTML('beforeend', imageHtml);
            };
            reader.readAsDataURL(file);
        });
    }
    
    static createPreviewContainer(input) {
        const container = document.createElement('div');
        container.className = 'image-preview mt-3';
        input.parentElement.appendChild(container);
        return container;
    }
    
    static removePreview(button) {
        const previewItem = button.closest('.preview-item');
        if (previewItem) {
            previewItem.remove();
        }
    }
}

// إدارة النماذج
class FormManager {
    static init() {
        // التحقق من صحة النماذج
        const forms = document.querySelectorAll('form[data-validate="true"]');
        forms.forEach(form => {
            form.addEventListener('submit', this.validateForm.bind(this));
        });
        
        // حفظ البيانات تلقائياً
        const autoSaveForms = document.querySelectorAll('form[data-autosave="true"]');
        autoSaveForms.forEach(form => {
            this.enableAutoSave(form);
        });
    }
    
    static validateForm(e) {
        const form = e.target;
        const requiredFields = form.querySelectorAll('[required]');
        let isValid = true;
        
        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                this.showFieldError(field, 'هذا الحقل مطلوب');
                isValid = false;
            } else {
                this.clearFieldError(field);
            }
        });
        
        // التحقق من البريد الإلكتروني
        const emailFields = form.querySelectorAll('input[type="email"]');
        emailFields.forEach(field => {
            if (field.value && !this.isValidEmail(field.value)) {
                this.showFieldError(field, 'البريد الإلكتروني غير صحيح');
                isValid = false;
            }
        });
        
        // التحقق من رقم الهاتف
        const phoneFields = form.querySelectorAll('input[type="tel"]');
        phoneFields.forEach(field => {
            if (field.value && !this.isValidPhone(field.value)) {
                this.showFieldError(field, 'رقم الهاتف غير صحيح');
                isValid = false;
            }
        });
        
        if (!isValid) {
            e.preventDefault();
            showAlert('يرجى تصحيح الأخطاء في النموذج', 'error');
        }
    }
    
    static showFieldError(field, message) {
        this.clearFieldError(field);
        
        field.classList.add('is-invalid');
        const errorDiv = document.createElement('div');
        errorDiv.className = 'invalid-feedback';
        errorDiv.textContent = message;
        field.parentElement.appendChild(errorDiv);
    }
    
    static clearFieldError(field) {
        field.classList.remove('is-invalid');
        const errorDiv = field.parentElement.querySelector('.invalid-feedback');
        if (errorDiv) {
            errorDiv.remove();
        }
    }
    
    static isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    
    static isValidPhone(phone) {
        const phoneRegex = /^[0-9+\-\s()]{10,15}$/;
        return phoneRegex.test(phone);
    }
    
    static enableAutoSave(form) {
        const inputs = form.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            input.addEventListener('input', () => {
                this.autoSave(form);
            });
        });
    }
    
    static autoSave(form) {
        const formData = new FormData(form);
        const data = {};
        for (let [key, value] of formData.entries()) {
            data[key] = value;
        }
        
        localStorage.setItem('form_autosave_' + form.id, JSON.stringify(data));
    }
    
    static restoreAutoSave(formId) {
        const savedData = localStorage.getItem('form_autosave_' + formId);
        if (savedData) {
            const data = JSON.parse(savedData);
            const form = document.getElementById(formId);
            
            if (form) {
                Object.keys(data).forEach(key => {
                    const field = form.querySelector(`[name="${key}"]`);
                    if (field) {
                        field.value = data[key];
                    }
                });
            }
        }
    }
}

// إدارة الإشعارات
class NotificationManager {
    static init() {
        this.checkPermission();
        this.loadNotifications();
    }
    
    static checkPermission() {
        if ('Notification' in window && Notification.permission === 'default') {
            Notification.requestPermission();
        }
    }
    
    static show(title, options = {}) {
        if ('Notification' in window && Notification.permission === 'granted') {
            new Notification(title, {
                icon: '/api/assets/images/logo.png',
                badge: '/api/assets/images/badge.png',
                ...options
            });
        }
    }
    
    static loadNotifications() {
        // تحميل الإشعارات من الخادم
        sendAjaxRequest(`${CONFIG.baseUrl}/ajax/notifications.php`, {}, 'GET')
        .then(response => {
            if (response.success) {
                this.displayNotifications(response.notifications);
            }
        })
        .catch(error => {
            console.error('خطأ في تحميل الإشعارات:', error);
        });
    }
    
    static displayNotifications(notifications) {
        const notificationBadge = document.querySelector('.notification-badge');
        if (notificationBadge && notifications.length > 0) {
            notificationBadge.textContent = notifications.length;
            notificationBadge.style.display = 'inline';
        }
    }
}

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة المكونات
    SearchManager.init();
    ImageUploader.init();
    FormManager.init();
    NotificationManager.init();
    
    // إضافة مستمعي الأحداث للمفضلة
    document.addEventListener('click', function(e) {
        if (e.target.closest('.favorite-btn')) {
            e.preventDefault();
            const button = e.target.closest('.favorite-btn');
            const adId = button.getAttribute('data-ad-id');
            FavoriteManager.toggle(adId, button);
        }
    });
    
    // تفعيل tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // تفعيل popovers
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    // إخفاء التنبيهات تلقائياً
    setTimeout(() => {
        const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
        alerts.forEach(alert => {
            if (alert.classList.contains('show')) {
                alert.classList.remove('show');
                setTimeout(() => alert.remove(), 300);
            }
        });
    }, 5000);
});

// تصدير الكائنات للاستخدام العام
window.ClassifiedAds = {
    CONFIG,
    showAlert,
    toggleLoading,
    sendAjaxRequest,
    FavoriteManager,
    SearchManager,
    ImageUploader,
    FormManager,
    NotificationManager
};
