<?php
/**
 * سايدبار لوحة الإدارة
 * Admin Panel Sidebar
 */

// التأكد من تحميل ملف التحقق من الصلاحيات
if (!function_exists('isAdminLoggedIn')) {
    require_once 'auth.php';
}

// الحصول على الصفحة الحالية
$currentPage = basename($_SERVER['PHP_SELF']);

// الحصول على إحصائيات سريعة للسايدبار
$db = Database::getInstance();
$sidebarStats = [
    'pending_ads' => $db->count('ads', ['status = ?'], ['pending']),
    'pending_reports' => $db->count('reports', ['status = ?'], ['pending']),
    'total_users' => $db->count('users', ['status = ?'], ['active']),
    'total_categories' => $db->count('categories', ['status = ?'], ['active'])
];

// تعريف عناصر القائمة
$menuItems = [
    [
        'title' => 'لوحة التحكم',
        'icon' => 'fas fa-tachometer-alt',
        'url' => 'index.php',
        'active' => $currentPage === 'index.php',
        'permission' => null
    ],
    [
        'title' => 'الإعلانات',
        'icon' => 'fas fa-bullhorn',
        'url' => 'ads.php',
        'active' => $currentPage === 'ads.php',
        'permission' => 'manage_ads',
        'badge' => $sidebarStats['pending_ads'] > 0 ? $sidebarStats['pending_ads'] : null,
        'badge_class' => 'bg-warning',
        'submenu' => [
            [
                'title' => 'جميع الإعلانات',
                'url' => 'ads.php',
                'permission' => 'manage_ads'
            ],
            [
                'title' => 'إعلانات معلقة',
                'url' => 'ads.php?status=pending',
                'permission' => 'manage_ads',
                'badge' => $sidebarStats['pending_ads']
            ],
            [
                'title' => 'إعلانات مميزة',
                'url' => 'ads.php?featured=1',
                'permission' => 'manage_ads'
            ],
            [
                'title' => 'إضافة إعلان',
                'url' => 'ads.php?action=add',
                'permission' => 'manage_ads'
            ]
        ]
    ],
    [
        'title' => 'المستخدمون',
        'icon' => 'fas fa-users',
        'url' => 'users.php',
        'active' => $currentPage === 'users.php',
        'permission' => 'manage_users',
        'badge' => $sidebarStats['total_users'],
        'badge_class' => 'bg-success',
        'submenu' => [
            [
                'title' => 'جميع المستخدمين',
                'url' => 'users.php',
                'permission' => 'manage_users'
            ],
            [
                'title' => 'مستخدمون نشطون',
                'url' => 'users.php?status=active',
                'permission' => 'manage_users'
            ],
            [
                'title' => 'مستخدمون محظورون',
                'url' => 'users.php?status=banned',
                'permission' => 'manage_users'
            ],
            [
                'title' => 'إضافة مستخدم',
                'url' => 'users.php?action=add',
                'permission' => 'manage_users'
            ]
        ]
    ],
    [
        'title' => 'الأقسام',
        'icon' => 'fas fa-folder',
        'url' => 'categories.php',
        'active' => $currentPage === 'categories.php',
        'permission' => 'manage_categories',
        'badge' => $sidebarStats['total_categories'],
        'badge_class' => 'bg-info',
        'submenu' => [
            [
                'title' => 'جميع الأقسام',
                'url' => 'categories.php',
                'permission' => 'manage_categories'
            ],
            [
                'title' => 'أقسام رئيسية',
                'url' => 'categories.php?type=parent',
                'permission' => 'manage_categories'
            ],
            [
                'title' => 'أقسام فرعية',
                'url' => 'categories.php?type=child',
                'permission' => 'manage_categories'
            ],
            [
                'title' => 'إضافة قسم',
                'url' => 'categories.php?action=add',
                'permission' => 'manage_categories'
            ]
        ]
    ],
    [
        'title' => 'التقارير',
        'icon' => 'fas fa-flag',
        'url' => 'reports.php',
        'active' => $currentPage === 'reports.php',
        'permission' => 'manage_reports',
        'badge' => $sidebarStats['pending_reports'] > 0 ? $sidebarStats['pending_reports'] : null,
        'badge_class' => 'bg-danger',
        'submenu' => [
            [
                'title' => 'جميع التقارير',
                'url' => 'reports.php',
                'permission' => 'manage_reports'
            ],
            [
                'title' => 'تقارير معلقة',
                'url' => 'reports.php?status=pending',
                'permission' => 'manage_reports',
                'badge' => $sidebarStats['pending_reports']
            ],
            [
                'title' => 'تقارير محلولة',
                'url' => 'reports.php?status=resolved',
                'permission' => 'manage_reports'
            ]
        ]
    ],
    [
        'title' => 'الرسائل',
        'icon' => 'fas fa-envelope',
        'url' => 'messages.php',
        'active' => $currentPage === 'messages.php',
        'permission' => 'manage_messages'
    ],
    [
        'title' => 'الصفحات',
        'icon' => 'fas fa-file-alt',
        'url' => 'pages.php',
        'active' => $currentPage === 'pages.php',
        'permission' => 'manage_pages',
        'submenu' => [
            [
                'title' => 'جميع الصفحات',
                'url' => 'pages.php',
                'permission' => 'manage_pages'
            ],
            [
                'title' => 'إضافة صفحة',
                'url' => 'pages.php?action=add',
                'permission' => 'manage_pages'
            ]
        ]
    ],
    [
        'title' => 'الإحصائيات',
        'icon' => 'fas fa-chart-bar',
        'url' => 'statistics.php',
        'active' => $currentPage === 'statistics.php',
        'permission' => 'view_statistics',
        'submenu' => [
            [
                'title' => 'إحصائيات عامة',
                'url' => 'statistics.php',
                'permission' => 'view_statistics'
            ],
            [
                'title' => 'إحصائيات الإعلانات',
                'url' => 'statistics.php?type=ads',
                'permission' => 'view_statistics'
            ],
            [
                'title' => 'إحصائيات المستخدمين',
                'url' => 'statistics.php?type=users',
                'permission' => 'view_statistics'
            ],
            [
                'title' => 'إحصائيات المشاهدات',
                'url' => 'statistics.php?type=views',
                'permission' => 'view_statistics'
            ]
        ]
    ]
];

// إضافة عناصر الإدارة للمدير الأعلى
if (hasAdminRole('super_admin') || hasAdminPermission('manage_admins')) {
    $menuItems[] = [
        'title' => 'المديرون',
        'icon' => 'fas fa-user-shield',
        'url' => 'admins.php',
        'active' => $currentPage === 'admins.php',
        'permission' => 'manage_admins',
        'submenu' => [
            [
                'title' => 'جميع المديرين',
                'url' => 'admins.php',
                'permission' => 'manage_admins'
            ],
            [
                'title' => 'إضافة مدير',
                'url' => 'admins.php?action=add',
                'permission' => 'manage_admins'
            ],
            [
                'title' => 'الأدوار والصلاحيات',
                'url' => 'roles.php',
                'permission' => 'manage_admins'
            ]
        ]
    ];
}

// إضافة عناصر الإعدادات
if (hasAdminRole('super_admin') || hasAdminPermission('manage_settings')) {
    $menuItems[] = [
        'title' => 'الإعدادات',
        'icon' => 'fas fa-cog',
        'url' => 'settings.php',
        'active' => $currentPage === 'settings.php',
        'permission' => 'manage_settings',
        'submenu' => [
            [
                'title' => 'إعدادات عامة',
                'url' => 'settings.php',
                'permission' => 'manage_settings'
            ],
            [
                'title' => 'إعدادات البريد',
                'url' => 'settings.php?tab=email',
                'permission' => 'manage_settings'
            ],
            [
                'title' => 'إعدادات الأمان',
                'url' => 'settings.php?tab=security',
                'permission' => 'manage_settings'
            ],
            [
                'title' => 'النسخ الاحتياطي',
                'url' => 'backup.php',
                'permission' => 'backup_restore'
            ]
        ]
    ];
}

// تصفية العناصر حسب الصلاحيات
$filteredMenuItems = array_filter($menuItems, function($item) {
    return $item['permission'] === null || hasAdminPermission($item['permission']);
});
?>

<aside class="admin-sidebar" id="adminSidebar">
    <!-- Sidebar Header -->
    <div class="admin-sidebar-header">
        <div class="text-center py-4">
            <div class="admin-sidebar-logo">
                <i class="fas fa-shield-alt fa-2x text-primary mb-2"></i>
                <h6 class="mb-0">لوحة الإدارة</h6>
                <small class="text-muted">
                    <?php 
                    $roles = getAvailableRoles();
                    echo $roles[$_SESSION['admin_role']] ?? $_SESSION['admin_role'];
                    ?>
                </small>
            </div>
        </div>
    </div>
    
    <!-- Sidebar Menu -->
    <nav class="admin-sidebar-nav">
        <ul class="admin-sidebar-menu">
            <?php foreach ($filteredMenuItems as $item): ?>
                <li class="admin-sidebar-item">
                    <a href="<?php echo adminUrl($item['url']); ?>" 
                       class="admin-sidebar-link <?php echo $item['active'] ? 'active' : ''; ?>"
                       <?php if (!empty($item['submenu'])): ?>
                           data-bs-toggle="collapse" 
                           data-bs-target="#submenu-<?php echo md5($item['title']); ?>"
                           aria-expanded="<?php echo $item['active'] ? 'true' : 'false'; ?>"
                       <?php endif; ?>>
                        
                        <span class="admin-sidebar-icon">
                            <i class="<?php echo $item['icon']; ?>"></i>
                        </span>
                        
                        <span class="admin-sidebar-text">
                            <?php echo $item['title']; ?>
                        </span>
                        
                        <?php if (!empty($item['badge'])): ?>
                            <span class="badge admin-sidebar-badge <?php echo $item['badge_class'] ?? 'bg-primary'; ?>">
                                <?php echo $item['badge']; ?>
                            </span>
                        <?php endif; ?>
                        
                        <?php if (!empty($item['submenu'])): ?>
                            <span class="admin-sidebar-arrow">
                                <i class="fas fa-chevron-down"></i>
                            </span>
                        <?php endif; ?>
                    </a>
                    
                    <?php if (!empty($item['submenu'])): ?>
                        <div class="collapse <?php echo $item['active'] ? 'show' : ''; ?>" 
                             id="submenu-<?php echo md5($item['title']); ?>">
                            <ul class="admin-sidebar-submenu">
                                <?php foreach ($item['submenu'] as $subitem): ?>
                                    <?php if ($subitem['permission'] === null || hasAdminPermission($subitem['permission'])): ?>
                                        <li class="admin-sidebar-subitem">
                                            <a href="<?php echo adminUrl($subitem['url']); ?>" 
                                               class="admin-sidebar-sublink">
                                                <?php echo $subitem['title']; ?>
                                                <?php if (!empty($subitem['badge'])): ?>
                                                    <span class="badge bg-warning ms-2">
                                                        <?php echo $subitem['badge']; ?>
                                                    </span>
                                                <?php endif; ?>
                                            </a>
                                        </li>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>
                </li>
            <?php endforeach; ?>
            
            <!-- Separator -->
            <li class="admin-sidebar-separator"></li>
            
            <!-- Quick Links -->
            <li class="admin-sidebar-item">
                <a href="<?php echo url(); ?>" target="_blank" class="admin-sidebar-link">
                    <span class="admin-sidebar-icon">
                        <i class="fas fa-external-link-alt"></i>
                    </span>
                    <span class="admin-sidebar-text">زيارة الموقع</span>
                </a>
            </li>
            
            <li class="admin-sidebar-item">
                <a href="<?php echo adminUrl('help.php'); ?>" class="admin-sidebar-link">
                    <span class="admin-sidebar-icon">
                        <i class="fas fa-question-circle"></i>
                    </span>
                    <span class="admin-sidebar-text">المساعدة</span>
                </a>
            </li>
        </ul>
    </nav>
    
    <!-- Sidebar Footer -->
    <div class="admin-sidebar-footer">
        <div class="p-3">
            <div class="admin-sidebar-user">
                <div class="d-flex align-items-center">
                    <?php if (!empty($currentAdmin['avatar'])): ?>
                        <img src="<?php echo upload_url($currentAdmin['avatar']); ?>" 
                             alt="صورة المدير" class="admin-avatar me-2">
                    <?php else: ?>
                        <div class="admin-avatar me-2 bg-primary d-flex align-items-center justify-content-center">
                            <i class="fas fa-user text-white"></i>
                        </div>
                    <?php endif; ?>
                    <div class="flex-grow-1">
                        <div class="fw-bold small">
                            <?php echo htmlspecialchars($_SESSION['admin_name']); ?>
                        </div>
                        <div class="text-muted small">
                            <i class="fas fa-circle text-success me-1" style="font-size: 0.5rem;"></i>
                            متصل
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="admin-sidebar-actions mt-3">
                <div class="btn-group w-100" role="group">
                    <a href="<?php echo adminUrl('profile.php'); ?>" 
                       class="btn btn-outline-primary btn-sm" title="الملف الشخصي">
                        <i class="fas fa-user"></i>
                    </a>
                    <a href="<?php echo adminUrl('settings.php'); ?>" 
                       class="btn btn-outline-secondary btn-sm" title="الإعدادات">
                        <i class="fas fa-cog"></i>
                    </a>
                    <a href="<?php echo adminUrl('logout.php'); ?>" 
                       class="btn btn-outline-danger btn-sm" title="تسجيل الخروج">
                        <i class="fas fa-sign-out-alt"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Sidebar Toggle Button -->
    <button class="admin-sidebar-toggle" id="sidebarCollapseToggle">
        <i class="fas fa-chevron-right"></i>
    </button>
</aside>

<style>
/* تحسينات إضافية للسايدبار */
.admin-sidebar {
    background: linear-gradient(180deg, #ffffff 0%, #f8f9fa 100%);
    border-left: 1px solid #e9ecef;
}

.admin-sidebar-header {
    border-bottom: 1px solid #e9ecef;
    background: rgba(52, 152, 219, 0.05);
}

.admin-sidebar-logo i {
    color: var(--admin-accent);
}

.admin-sidebar-menu {
    padding: 10px 0;
}

.admin-sidebar-link {
    position: relative;
    overflow: hidden;
}

.admin-sidebar-link::before {
    content: '';
    position: absolute;
    top: 0;
    right: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(52, 152, 219, 0.1), transparent);
    transition: right 0.5s;
}

.admin-sidebar-link:hover::before {
    right: 100%;
}

.admin-sidebar-arrow {
    margin-right: auto;
    transition: transform 0.3s ease;
}

.admin-sidebar-link[aria-expanded="true"] .admin-sidebar-arrow {
    transform: rotate(180deg);
}

.admin-sidebar-submenu {
    list-style: none;
    padding: 0;
    margin: 0;
    background: rgba(52, 152, 219, 0.05);
}

.admin-sidebar-sublink {
    display: block;
    padding: 10px 20px 10px 60px;
    color: #666;
    text-decoration: none;
    font-size: 0.9rem;
    transition: var(--admin-transition);
    border-right: 3px solid transparent;
}

.admin-sidebar-sublink:hover {
    background: rgba(52, 152, 219, 0.1);
    color: var(--admin-accent);
    border-right-color: var(--admin-accent);
    text-decoration: none;
}

.admin-sidebar-separator {
    height: 1px;
    background: #e9ecef;
    margin: 10px 20px;
}

.admin-sidebar-footer {
    border-top: 1px solid #e9ecef;
    background: rgba(52, 152, 219, 0.05);
    margin-top: auto;
}

.admin-sidebar-toggle {
    position: absolute;
    top: 50%;
    left: -15px;
    transform: translateY(-50%);
    width: 30px;
    height: 30px;
    background: var(--admin-accent);
    color: white;
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--admin-box-shadow);
    transition: var(--admin-transition);
    z-index: 1001;
}

.admin-sidebar-toggle:hover {
    background: var(--admin-primary);
    transform: translateY(-50%) scale(1.1);
}

.admin-sidebar.collapsed .admin-sidebar-toggle i {
    transform: rotate(180deg);
}

.admin-sidebar.collapsed .admin-sidebar-header,
.admin-sidebar.collapsed .admin-sidebar-footer {
    padding: 10px;
}

.admin-sidebar.collapsed .admin-sidebar-user {
    text-align: center;
}

.admin-sidebar.collapsed .admin-sidebar-user .flex-grow-1 {
    display: none;
}

.admin-sidebar.collapsed .admin-sidebar-actions {
    flex-direction: column;
}

.admin-sidebar.collapsed .admin-sidebar-actions .btn {
    margin-bottom: 5px;
    border-radius: 50% !important;
    width: 35px;
    height: 35px;
    padding: 0;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .admin-sidebar {
        transform: translateX(100%);
        z-index: 1050;
    }
    
    .admin-sidebar.show {
        transform: translateX(0);
    }
    
    .admin-sidebar-toggle {
        display: none;
    }
}

/* تحسين إمكانية الوصول */
.admin-sidebar-link:focus,
.admin-sidebar-sublink:focus {
    outline: 2px solid var(--admin-accent);
    outline-offset: -2px;
}

/* تأثيرات الحركة */
.admin-sidebar-submenu {
    transition: all 0.3s ease;
}

.collapse:not(.show) .admin-sidebar-submenu {
    opacity: 0;
}

.collapse.show .admin-sidebar-submenu {
    opacity: 1;
}

/* تحسين الطباعة */
@media print {
    .admin-sidebar {
        display: none !important;
    }
}
</style>

<script>
// وظائف السايدبار
document.addEventListener('DOMContentLoaded', function() {
    // تبديل طي/فتح السايدبار
    const sidebarToggle = document.getElementById('sidebarCollapseToggle');
    const sidebar = document.getElementById('adminSidebar');
    
    if (sidebarToggle && sidebar) {
        sidebarToggle.addEventListener('click', function() {
            sidebar.classList.toggle('collapsed');
            
            // حفظ حالة السايدبار في localStorage
            const isCollapsed = sidebar.classList.contains('collapsed');
            localStorage.setItem('admin_sidebar_collapsed', isCollapsed);
        });
        
        // استعادة حالة السايدبار من localStorage
        const savedState = localStorage.getItem('admin_sidebar_collapsed');
        if (savedState === 'true') {
            sidebar.classList.add('collapsed');
        }
    }
    
    // تفعيل tooltips للسايدبار المطوي
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('.admin-sidebar.collapsed [data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // إضافة tooltips للسايدبار المطوي
    sidebar.addEventListener('transitionend', function() {
        if (this.classList.contains('collapsed')) {
            // إضافة tooltips للعناصر
            this.querySelectorAll('.admin-sidebar-link').forEach(link => {
                const text = link.querySelector('.admin-sidebar-text').textContent;
                link.setAttribute('data-bs-toggle', 'tooltip');
                link.setAttribute('data-bs-placement', 'left');
                link.setAttribute('title', text);
            });
        } else {
            // إزالة tooltips
            this.querySelectorAll('.admin-sidebar-link').forEach(link => {
                link.removeAttribute('data-bs-toggle');
                link.removeAttribute('data-bs-placement');
                link.removeAttribute('title');
            });
        }
    });
    
    // تمييز العنصر النشط
    const currentUrl = window.location.pathname;
    const sidebarLinks = document.querySelectorAll('.admin-sidebar-link, .admin-sidebar-sublink');
    
    sidebarLinks.forEach(link => {
        if (link.getAttribute('href') && currentUrl.includes(link.getAttribute('href'))) {
            link.classList.add('active');
            
            // فتح القائمة الفرعية إذا كان العنصر فيها
            const parentCollapse = link.closest('.collapse');
            if (parentCollapse) {
                parentCollapse.classList.add('show');
                const parentLink = document.querySelector(`[data-bs-target="#${parentCollapse.id}"]`);
                if (parentLink) {
                    parentLink.setAttribute('aria-expanded', 'true');
                }
            }
        }
    });
    
    // تحديث الشارات كل دقيقة
    setInterval(function() {
        updateSidebarBadges();
    }, 60000);
    
    // دالة تحديث الشارات
    function updateSidebarBadges() {
        fetch('<?php echo adminUrl("ajax/sidebar-stats.php"); ?>')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // تحديث شارة الإعلانات المعلقة
                    const adsBadge = document.querySelector('[href*="ads.php"] .admin-sidebar-badge');
                    if (adsBadge && data.stats.pending_ads > 0) {
                        adsBadge.textContent = data.stats.pending_ads;
                        adsBadge.style.display = 'inline';
                    } else if (adsBadge) {
                        adsBadge.style.display = 'none';
                    }
                    
                    // تحديث شارة التقارير المعلقة
                    const reportsBadge = document.querySelector('[href*="reports.php"] .admin-sidebar-badge');
                    if (reportsBadge && data.stats.pending_reports > 0) {
                        reportsBadge.textContent = data.stats.pending_reports;
                        reportsBadge.style.display = 'inline';
                    } else if (reportsBadge) {
                        reportsBadge.style.display = 'none';
                    }
                }
            })
            .catch(error => console.log('خطأ في تحديث شارات السايدبار:', error));
    }
});
</script>
