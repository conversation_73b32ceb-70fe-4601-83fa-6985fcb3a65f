<?php
/**
 * ملف الفوتر المشترك
 * Shared Footer File
 */

// التأكد من تحميل الملفات المطلوبة
if (!defined('SITE_ROOT')) {
    require_once 'config.php';
    require_once 'functions.php';
}

// الحصول على الأقسام الرئيسية للفوتر
$footerCategories = getCategories(null, 'active');
$footerCategories = array_slice($footerCategories, 0, 6); // أول 6 أقسام فقط

// الحصول على إحصائيات الموقع
$db = Database::getInstance();
$footerStats = [
    'total_ads' => $db->count('ads', ['status = ?'], ['active']),
    'total_users' => $db->count('users', ['status = ?'], ['active']),
    'total_categories' => $db->count('categories', ['status = ?'], ['active'])
];
?>

<footer class="footer bg-dark text-light py-5 mt-5">
    <div class="container">
        <div class="row">
            <!-- معلومات الموقع -->
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="footer-section">
                    <h5 class="footer-title">
                        <i class="fas fa-bullhorn me-2"></i>
                        <?php echo getSetting('site_name', 'موقع الإعلانات'); ?>
                    </h5>
                    <p class="footer-description">
                        <?php echo getSetting('site_description', 'موقع إعلانات مبوبة شامل يوفر منصة آمنة وسهلة للبيع والشراء في جميع أنحاء المملكة العربية السعودية.'); ?>
                    </p>
                    
                    <!-- إحصائيات سريعة -->
                    <div class="footer-stats">
                        <div class="row text-center">
                            <div class="col-4">
                                <div class="stat-item">
                                    <h6 class="stat-number"><?php echo formatNumber($footerStats['total_ads']); ?></h6>
                                    <small class="stat-label">إعلان نشط</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="stat-item">
                                    <h6 class="stat-number"><?php echo formatNumber($footerStats['total_users']); ?></h6>
                                    <small class="stat-label">مستخدم</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="stat-item">
                                    <h6 class="stat-number"><?php echo formatNumber($footerStats['total_categories']); ?></h6>
                                    <small class="stat-label">قسم</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- روابط التواصل الاجتماعي -->
                    <div class="social-links mt-4">
                        <h6 class="mb-3">تابعنا على</h6>
                        <div class="social-icons">
                            <a href="#" class="social-link" title="فيسبوك" target="_blank">
                                <i class="fab fa-facebook"></i>
                            </a>
                            <a href="#" class="social-link" title="تويتر" target="_blank">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="#" class="social-link" title="إنستغرام" target="_blank">
                                <i class="fab fa-instagram"></i>
                            </a>
                            <a href="#" class="social-link" title="لينكد إن" target="_blank">
                                <i class="fab fa-linkedin"></i>
                            </a>
                            <a href="#" class="social-link" title="يوتيوب" target="_blank">
                                <i class="fab fa-youtube"></i>
                            </a>
                            <a href="#" class="social-link" title="تيك توك" target="_blank">
                                <i class="fab fa-tiktok"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- روابط سريعة -->
            <div class="col-lg-2 col-md-6 mb-4">
                <div class="footer-section">
                    <h6 class="footer-title">روابط سريعة</h6>
                    <ul class="footer-links">
                        <li><a href="<?php echo url(); ?>">الرئيسية</a></li>
                        <li><a href="<?php echo url('ads.php'); ?>">جميع الإعلانات</a></li>
                        <li><a href="<?php echo url('add-ad.php'); ?>">إضافة إعلان</a></li>
                        <li><a href="<?php echo url('search.php'); ?>">البحث المتقدم</a></li>
                        <li><a href="<?php echo url('contact.php'); ?>">اتصل بنا</a></li>
                        <li><a href="<?php echo url('about.php'); ?>">من نحن</a></li>
                    </ul>
                </div>
            </div>
            
            <!-- الأقسام الرئيسية -->
            <div class="col-lg-2 col-md-6 mb-4">
                <div class="footer-section">
                    <h6 class="footer-title">الأقسام الرئيسية</h6>
                    <ul class="footer-links">
                        <?php foreach ($footerCategories as $category): ?>
                            <li>
                                <a href="<?php echo url('category.php?id=' . $category['id']); ?>">
                                    <i class="<?php echo $category['icon']; ?> me-2"></i>
                                    <?php echo $category['name_ar']; ?>
                                </a>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            </div>
            
            <!-- حساب المستخدم -->
            <div class="col-lg-2 col-md-6 mb-4">
                <div class="footer-section">
                    <h6 class="footer-title">حسابك</h6>
                    <ul class="footer-links">
                        <?php if (isLoggedIn()): ?>
                            <li><a href="<?php echo url('profile.php'); ?>">حسابي</a></li>
                            <li><a href="<?php echo url('my-ads.php'); ?>">إعلاناتي</a></li>
                            <li><a href="<?php echo url('favorites.php'); ?>">المفضلة</a></li>
                            <li><a href="<?php echo url('messages.php'); ?>">الرسائل</a></li>
                            <li><a href="<?php echo url('settings.php'); ?>">الإعدادات</a></li>
                            <li><a href="<?php echo url('logout.php'); ?>">تسجيل الخروج</a></li>
                        <?php else: ?>
                            <li><a href="<?php echo url('login.php'); ?>">تسجيل الدخول</a></li>
                            <li><a href="<?php echo url('register.php'); ?>">إنشاء حساب جديد</a></li>
                            <li><a href="<?php echo url('forgot-password.php'); ?>">نسيت كلمة المرور؟</a></li>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>
            
            <!-- معلومات الاتصال -->
            <div class="col-lg-2 col-md-6 mb-4">
                <div class="footer-section">
                    <h6 class="footer-title">اتصل بنا</h6>
                    <div class="contact-info">
                        <div class="contact-item">
                            <i class="fas fa-envelope me-2"></i>
                            <a href="mailto:<?php echo getSetting('admin_email', '<EMAIL>'); ?>">
                                <?php echo getSetting('admin_email', '<EMAIL>'); ?>
                            </a>
                        </div>
                        
                        <div class="contact-item">
                            <i class="fas fa-phone me-2"></i>
                            <a href="tel:+966501234567">+966 50 123 4567</a>
                        </div>
                        
                        <div class="contact-item">
                            <i class="fab fa-whatsapp me-2"></i>
                            <a href="https://wa.me/966501234567" target="_blank">واتساب</a>
                        </div>
                        
                        <div class="contact-item">
                            <i class="fas fa-map-marker-alt me-2"></i>
                            <span>الرياض، المملكة العربية السعودية</span>
                        </div>
                        
                        <div class="contact-item">
                            <i class="fas fa-clock me-2"></i>
                            <span>24/7 خدمة العملاء</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Newsletter Subscription -->
        <div class="row mt-4">
            <div class="col-lg-8 mx-auto">
                <div class="newsletter-section text-center">
                    <h6 class="mb-3">اشترك في النشرة الإخبارية</h6>
                    <p class="mb-3">احصل على آخر الإعلانات والعروض المميزة مباشرة في بريدك الإلكتروني</p>
                    <form class="newsletter-form" action="<?php echo url('ajax/newsletter.php'); ?>" method="POST">
                        <div class="input-group">
                            <input type="email" class="form-control" name="email" 
                                   placeholder="أدخل بريدك الإلكتروني..." required>
                            <button class="btn btn-primary" type="submit">
                                <i class="fas fa-paper-plane me-1"></i>
                                اشتراك
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Separator -->
        <hr class="my-4 border-secondary">
        
        <!-- Bottom Footer -->
        <div class="row align-items-center">
            <div class="col-md-6">
                <p class="copyright mb-0">
                    &copy; <?php echo date('Y'); ?> 
                    <strong><?php echo getSetting('site_name', 'موقع الإعلانات'); ?></strong>. 
                    جميع الحقوق محفوظة.
                </p>
            </div>
            
            <div class="col-md-6 text-md-end">
                <div class="footer-legal-links">
                    <a href="<?php echo url('privacy.php'); ?>" class="legal-link">سياسة الخصوصية</a>
                    <a href="<?php echo url('terms.php'); ?>" class="legal-link">شروط الاستخدام</a>
                    <a href="<?php echo url('help.php'); ?>" class="legal-link">المساعدة</a>
                    <a href="<?php echo url('sitemap.php'); ?>" class="legal-link">خريطة الموقع</a>
                </div>
            </div>
        </div>
        
        <!-- Back to Top Button -->
        <button class="back-to-top" id="backToTop" title="العودة للأعلى">
            <i class="fas fa-chevron-up"></i>
        </button>
    </div>
</footer>

<style>
/* تصميم الفوتر */
.footer {
    background: linear-gradient(135deg, #2c3e50, #34495e) !important;
    position: relative;
    overflow: hidden;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.footer .container {
    position: relative;
    z-index: 1;
}

.footer-title {
    color: #fff;
    margin-bottom: 20px;
    font-weight: 600;
    position: relative;
}

.footer-title::after {
    content: '';
    position: absolute;
    bottom: -5px;
    right: 0;
    width: 30px;
    height: 2px;
    background: #007bff;
    border-radius: 1px;
}

.footer-description {
    color: #bdc3c7;
    line-height: 1.6;
    margin-bottom: 20px;
}

.footer-stats .stat-item {
    padding: 10px 5px;
}

.footer-stats .stat-number {
    color: #007bff;
    font-weight: bold;
    margin-bottom: 5px;
}

.footer-stats .stat-label {
    color: #95a5a6;
}

.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links li {
    margin-bottom: 8px;
}

.footer-links a {
    color: #bdc3c7;
    text-decoration: none;
    transition: all 0.3s ease;
    display: inline-block;
}

.footer-links a:hover {
    color: #fff;
    transform: translateX(5px);
}

.social-icons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.social-link {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    color: #bdc3c7;
    border-radius: 50%;
    text-decoration: none;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.social-link:hover {
    background: #007bff;
    color: #fff;
    transform: translateY(-3px);
}

.contact-info .contact-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    color: #bdc3c7;
}

.contact-info .contact-item i {
    width: 20px;
    color: #007bff;
}

.contact-info .contact-item a {
    color: #bdc3c7;
    text-decoration: none;
    transition: color 0.3s ease;
}

.contact-info .contact-item a:hover {
    color: #fff;
}

.newsletter-section {
    background: rgba(255, 255, 255, 0.05);
    padding: 30px;
    border-radius: 10px;
    backdrop-filter: blur(10px);
}

.newsletter-form .input-group {
    max-width: 400px;
    margin: 0 auto;
}

.newsletter-form .form-control {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #fff;
}

.newsletter-form .form-control::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.newsletter-form .form-control:focus {
    background: rgba(255, 255, 255, 0.15);
    border-color: #007bff;
    color: #fff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.copyright {
    color: #95a5a6;
}

.footer-legal-links {
    display: flex;
    gap: 20px;
    justify-content: flex-end;
    flex-wrap: wrap;
}

.legal-link {
    color: #bdc3c7;
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.legal-link:hover {
    color: #fff;
}

.back-to-top {
    position: fixed;
    bottom: 30px;
    left: 30px;
    width: 50px;
    height: 50px;
    background: #007bff;
    color: #fff;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: none;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    z-index: 1000;
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

.back-to-top:hover {
    background: #0056b3;
    transform: translateY(-3px);
}

.back-to-top.show {
    display: flex;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .footer-stats .row {
        text-align: center;
    }
    
    .social-icons {
        justify-content: center;
    }
    
    .footer-legal-links {
        justify-content: center;
        margin-top: 15px;
    }
    
    .newsletter-form .input-group {
        flex-direction: column;
    }
    
    .newsletter-form .form-control,
    .newsletter-form .btn {
        border-radius: 5px !important;
        margin-bottom: 10px;
    }
    
    .back-to-top {
        bottom: 20px;
        left: 20px;
        width: 45px;
        height: 45px;
    }
}

/* تحسين إمكانية الوصول */
.footer a:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

.back-to-top:focus {
    outline: 2px solid #fff;
    outline-offset: 2px;
}

/* تحسين الطباعة */
@media print {
    .footer {
        background: #fff !important;
        color: #000 !important;
    }
    
    .footer * {
        color: #000 !important;
    }
    
    .social-icons,
    .newsletter-section,
    .back-to-top {
        display: none !important;
    }
}
</style>

<script>
// وظائف الفوتر
document.addEventListener('DOMContentLoaded', function() {
    // زر العودة للأعلى
    const backToTopBtn = document.getElementById('backToTop');
    
    window.addEventListener('scroll', function() {
        if (window.pageYOffset > 300) {
            backToTopBtn.classList.add('show');
        } else {
            backToTopBtn.classList.remove('show');
        }
    });
    
    backToTopBtn.addEventListener('click', function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
    
    // نموذج النشرة الإخبارية
    const newsletterForm = document.querySelector('.newsletter-form');
    if (newsletterForm) {
        newsletterForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            
            // إظهار مؤشر التحميل
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري الإرسال...';
            submitBtn.disabled = true;
            
            fetch(this.action, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    ClassifiedAds.showAlert('تم الاشتراك بنجاح في النشرة الإخبارية!', 'success');
                    this.reset();
                } else {
                    ClassifiedAds.showAlert(data.message || 'حدث خطأ أثناء الاشتراك', 'error');
                }
            })
            .catch(error => {
                ClassifiedAds.showAlert('حدث خطأ في الشبكة', 'error');
            })
            .finally(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
        });
    }
    
    // تتبع النقرات على الروابط
    document.querySelectorAll('.footer a').forEach(link => {
        link.addEventListener('click', function() {
            // تتبع النقرات للإحصائيات
            if (typeof gtag !== 'undefined') {
                gtag('event', 'click', {
                    event_category: 'Footer',
                    event_label: this.textContent.trim()
                });
            }
        });
    });
});
</script>
