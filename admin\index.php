<?php
/**
 * الصفحة الرئيسية للوحة الإدارة
 * Admin Dashboard Homepage
 */

require_once 'includes/auth.php';

// التحقق من تسجيل الدخول
requireAdminLogin();

// الحصول على الإحصائيات
$stats = getAdminDashboardStats();

// الحصول على أحدث الإعلانات المعلقة
$db = Database::getInstance();
$pendingAds = $db->query("
    SELECT a.*, u.full_name as user_name, c.name_ar as category_name
    FROM ads a
    LEFT JOIN users u ON a.user_id = u.id
    LEFT JOIN categories c ON a.category_id = c.id
    WHERE a.status = 'pending'
    ORDER BY a.created_at DESC
    LIMIT 5
")->fetchAll();

// الحصول على أحدث المستخدمين
$newUsers = $db->query("
    SELECT * FROM users
    WHERE status = 'active'
    ORDER BY created_at DESC
    LIMIT 5
")->fetchAll();

// الحصول على أحدث التقارير
$recentReports = $db->query("
    SELECT r.*, a.title as ad_title, u.full_name as reporter_name
    FROM reports r
    LEFT JOIN ads a ON r.ad_id = a.id
    LEFT JOIN users u ON r.reporter_id = u.id
    WHERE r.status = 'pending'
    ORDER BY r.created_at DESC
    LIMIT 5
")->fetchAll();

// الحصول على إحصائيات الأنشطة الأخيرة
$recentActivities = $db->query("
    SELECT * FROM admin_activity_log
    ORDER BY created_at DESC
    LIMIT 10
")->fetchAll();

$pageTitle = 'لوحة التحكم الرئيسية';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - لوحة الإدارة</title>
    <meta name="robots" content="noindex, nofollow">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Admin CSS -->
    <link href="<?php echo asset('css/admin.css'); ?>" rel="stylesheet">
</head>
<body class="admin-body">
    <!-- Include Admin Header -->
    <?php include 'includes/header.php'; ?>
    
    <div class="admin-wrapper">
        <!-- Include Admin Sidebar -->
        <?php include 'includes/sidebar.php'; ?>
        
        <!-- Main Content -->
        <div class="admin-content">
            <div class="container-fluid">
                <!-- Page Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h1 class="admin-page-title">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            لوحة التحكم الرئيسية
                        </h1>
                        <p class="admin-page-subtitle">
                            مرحباً <?php echo htmlspecialchars($_SESSION['admin_name']); ?>، 
                            إليك نظرة عامة على إحصائيات الموقع
                        </p>
                    </div>
                    <div class="admin-page-actions">
                        <button class="btn btn-primary" onclick="refreshDashboard()">
                            <i class="fas fa-sync-alt me-1"></i>
                            تحديث
                        </button>
                    </div>
                </div>
                
                <!-- Session Messages -->
                <?php echo showAdminSessionMessages(); ?>
                
                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <!-- إحصائيات الإعلانات -->
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="stats-card stats-primary">
                            <div class="stats-card-body">
                                <div class="stats-icon">
                                    <i class="fas fa-bullhorn"></i>
                                </div>
                                <div class="stats-content">
                                    <h3 class="stats-number"><?php echo formatNumber($stats['total_ads']); ?></h3>
                                    <p class="stats-label">إجمالي الإعلانات</p>
                                    <div class="stats-details">
                                        <span class="badge bg-success"><?php echo $stats['active_ads']; ?> نشط</span>
                                        <span class="badge bg-warning"><?php echo $stats['pending_ads']; ?> معلق</span>
                                    </div>
                                </div>
                            </div>
                            <div class="stats-footer">
                                <a href="<?php echo adminUrl('ads.php'); ?>" class="stats-link">
                                    عرض جميع الإعلانات <i class="fas fa-arrow-left"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- إحصائيات المستخدمين -->
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="stats-card stats-success">
                            <div class="stats-card-body">
                                <div class="stats-icon">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div class="stats-content">
                                    <h3 class="stats-number"><?php echo formatNumber($stats['total_users']); ?></h3>
                                    <p class="stats-label">إجمالي المستخدمين</p>
                                    <div class="stats-details">
                                        <span class="badge bg-info"><?php echo $stats['new_users_today']; ?> جديد اليوم</span>
                                        <span class="badge bg-primary"><?php echo $stats['active_users']; ?> نشط</span>
                                    </div>
                                </div>
                            </div>
                            <div class="stats-footer">
                                <a href="<?php echo adminUrl('users.php'); ?>" class="stats-link">
                                    إدارة المستخدمين <i class="fas fa-arrow-left"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- إحصائيات المشاهدات -->
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="stats-card stats-info">
                            <div class="stats-card-body">
                                <div class="stats-icon">
                                    <i class="fas fa-eye"></i>
                                </div>
                                <div class="stats-content">
                                    <h3 class="stats-number"><?php echo formatNumber($stats['total_views']); ?></h3>
                                    <p class="stats-label">إجمالي المشاهدات</p>
                                    <div class="stats-details">
                                        <span class="badge bg-success"><?php echo $stats['views_today']; ?> اليوم</span>
                                        <span class="badge bg-warning"><?php echo $stats['featured_ads']; ?> مميز</span>
                                    </div>
                                </div>
                            </div>
                            <div class="stats-footer">
                                <a href="<?php echo adminUrl('statistics.php'); ?>" class="stats-link">
                                    عرض الإحصائيات <i class="fas fa-arrow-left"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- إحصائيات التقارير -->
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="stats-card stats-warning">
                            <div class="stats-card-body">
                                <div class="stats-icon">
                                    <i class="fas fa-flag"></i>
                                </div>
                                <div class="stats-content">
                                    <h3 class="stats-number"><?php echo formatNumber($stats['total_reports']); ?></h3>
                                    <p class="stats-label">إجمالي التقارير</p>
                                    <div class="stats-details">
                                        <span class="badge bg-danger"><?php echo $stats['pending_reports']; ?> معلق</span>
                                        <span class="badge bg-secondary"><?php echo $stats['total_categories']; ?> قسم</span>
                                    </div>
                                </div>
                            </div>
                            <div class="stats-footer">
                                <a href="<?php echo adminUrl('reports.php'); ?>" class="stats-link">
                                    مراجعة التقارير <i class="fas fa-arrow-left"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Charts Row -->
                <div class="row mb-4">
                    <div class="col-lg-8 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-chart-line me-2"></i>
                                    إحصائيات الإعلانات (آخر 7 أيام)
                                </h5>
                            </div>
                            <div class="card-body">
                                <canvas id="adsChart" height="100"></canvas>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-4 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-chart-pie me-2"></i>
                                    توزيع الإعلانات حسب الحالة
                                </h5>
                            </div>
                            <div class="card-body">
                                <canvas id="statusChart" height="200"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Content Row -->
                <div class="row">
                    <!-- الإعلانات المعلقة -->
                    <div class="col-lg-6 mb-4">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-clock me-2"></i>
                                    الإعلانات المعلقة
                                </h5>
                                <span class="badge bg-warning"><?php echo count($pendingAds); ?></span>
                            </div>
                            <div class="card-body">
                                <?php if (!empty($pendingAds)): ?>
                                    <div class="list-group list-group-flush">
                                        <?php foreach ($pendingAds as $ad): ?>
                                            <div class="list-group-item d-flex justify-content-between align-items-start">
                                                <div class="ms-2 me-auto">
                                                    <div class="fw-bold">
                                                        <?php echo htmlspecialchars($ad['title']); ?>
                                                    </div>
                                                    <small class="text-muted">
                                                        بواسطة: <?php echo htmlspecialchars($ad['user_name']); ?> |
                                                        القسم: <?php echo htmlspecialchars($ad['category_name']); ?>
                                                    </small>
                                                </div>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="<?php echo adminUrl('ads.php?action=view&id=' . $ad['id']); ?>" 
                                                       class="btn btn-outline-primary btn-sm">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="<?php echo adminUrl('ads.php?action=approve&id=' . $ad['id']); ?>" 
                                                       class="btn btn-outline-success btn-sm">
                                                        <i class="fas fa-check"></i>
                                                    </a>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                    <div class="card-footer text-center">
                                        <a href="<?php echo adminUrl('ads.php?status=pending'); ?>" class="btn btn-primary btn-sm">
                                            عرض جميع الإعلانات المعلقة
                                        </a>
                                    </div>
                                <?php else: ?>
                                    <div class="text-center py-4">
                                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                                        <p class="text-muted">لا توجد إعلانات معلقة</p>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <!-- المستخدمون الجدد -->
                    <div class="col-lg-6 mb-4">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-user-plus me-2"></i>
                                    المستخدمون الجدد
                                </h5>
                                <span class="badge bg-success"><?php echo count($newUsers); ?></span>
                            </div>
                            <div class="card-body">
                                <?php if (!empty($newUsers)): ?>
                                    <div class="list-group list-group-flush">
                                        <?php foreach ($newUsers as $user): ?>
                                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                                <div>
                                                    <div class="fw-bold">
                                                        <?php echo htmlspecialchars($user['full_name']); ?>
                                                    </div>
                                                    <small class="text-muted">
                                                        <?php echo htmlspecialchars($user['email']); ?> |
                                                        <?php echo timeAgo($user['created_at']); ?>
                                                    </small>
                                                </div>
                                                <a href="<?php echo adminUrl('users.php?action=view&id=' . $user['id']); ?>" 
                                                   class="btn btn-outline-primary btn-sm">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                    <div class="card-footer text-center">
                                        <a href="<?php echo adminUrl('users.php'); ?>" class="btn btn-primary btn-sm">
                                            عرض جميع المستخدمين
                                        </a>
                                    </div>
                                <?php else: ?>
                                    <div class="text-center py-4">
                                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">لا يوجد مستخدمون جدد</p>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Recent Reports -->
                <?php if (!empty($recentReports)): ?>
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    التقارير الأخيرة
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>الإعلان</th>
                                                <th>المبلغ</th>
                                                <th>السبب</th>
                                                <th>التاريخ</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($recentReports as $report): ?>
                                                <tr>
                                                    <td>
                                                        <strong><?php echo htmlspecialchars($report['ad_title']); ?></strong>
                                                    </td>
                                                    <td>
                                                        <?php echo htmlspecialchars($report['reporter_name'] ?: 'مجهول'); ?>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-warning">
                                                            <?php echo htmlspecialchars($report['reason']); ?>
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <?php echo timeAgo($report['created_at']); ?>
                                                    </td>
                                                    <td>
                                                        <a href="<?php echo adminUrl('reports.php?action=view&id=' . $report['id']); ?>" 
                                                           class="btn btn-outline-primary btn-sm">
                                                            مراجعة
                                                        </a>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Include Admin Footer -->
    <?php include 'includes/footer.php'; ?>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Admin JS -->
    <script src="<?php echo asset('js/admin.js'); ?>"></script>
    
    <script>
        // إعداد الرسوم البيانية
        document.addEventListener('DOMContentLoaded', function() {
            // رسم بياني للإعلانات
            const adsCtx = document.getElementById('adsChart').getContext('2d');
            new Chart(adsCtx, {
                type: 'line',
                data: {
                    labels: ['6 أيام', '5 أيام', '4 أيام', '3 أيام', 'أمس', 'اليوم'],
                    datasets: [{
                        label: 'إعلانات جديدة',
                        data: [12, 19, 8, 15, 25, 18],
                        borderColor: '#007bff',
                        backgroundColor: 'rgba(0, 123, 255, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
            
            // رسم بياني دائري لحالة الإعلانات
            const statusCtx = document.getElementById('statusChart').getContext('2d');
            new Chart(statusCtx, {
                type: 'doughnut',
                data: {
                    labels: ['نشط', 'معلق', 'مرفوض'],
                    datasets: [{
                        data: [<?php echo $stats['active_ads']; ?>, <?php echo $stats['pending_ads']; ?>, <?php echo $stats['rejected_ads']; ?>],
                        backgroundColor: ['#28a745', '#ffc107', '#dc3545']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        });
        
        // دالة تحديث لوحة التحكم
        function refreshDashboard() {
            location.reload();
        }
        
        // تحديث تلقائي كل 5 دقائق
        setInterval(function() {
            // يمكن إضافة تحديث AJAX هنا
        }, 300000);
    </script>
</body>
</html>
