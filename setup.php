<?php
/**
 * ملف إعداد قاعدة البيانات
 * Database Setup File
 */

echo "<h1>إعداد قاعدة البيانات</h1>";

try {
    // الاتصال بـ MySQL بدون تحديد قاعدة بيانات
    $pdo = new PDO('mysql:host=localhost;charset=utf8mb4', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p>✓ تم الاتصال بـ MySQL بنجاح</p>";
    
    // إنشاء قاعدة البيانات
    $pdo->exec("CREATE DATABASE IF NOT EXISTS classified_ads CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "<p>✓ تم إنشاء قاعدة البيانات classified_ads</p>";
    
    // الاتصال بقاعدة البيانات الجديدة
    $pdo = new PDO('mysql:host=localhost;dbname=classified_ads;charset=utf8mb4', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p>✓ تم الاتصال بقاعدة البيانات classified_ads</p>";
    
    // قراءة وتنفيذ ملف SQL
    if (file_exists('sql/database.sql')) {
        $sql = file_get_contents('sql/database.sql');
        
        // تقسيم الاستعلامات
        $statements = explode(';', $sql);
        
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement) && !preg_match('/^(\/\*|--|#)/', $statement)) {
                try {
                    $pdo->exec($statement);
                } catch (PDOException $e) {
                    // تجاهل أخطاء الجداول الموجودة
                    if (strpos($e->getMessage(), 'already exists') === false) {
                        echo "<p style='color: orange;'>تحذير: " . $e->getMessage() . "</p>";
                    }
                }
            }
        }
        
        echo "<p>✓ تم تنفيذ ملف SQL بنجاح</p>";
    } else {
        echo "<p style='color: red;'>✗ ملف sql/database.sql غير موجود</p>";
    }
    
    // فحص الجداول المنشأة
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    echo "<p>✓ تم إنشاء " . count($tables) . " جدول:</p>";
    echo "<ul>";
    foreach ($tables as $table) {
        echo "<li>" . $table . "</li>";
    }
    echo "</ul>";
    
    echo "<h2 style='color: green;'>تم إعداد قاعدة البيانات بنجاح!</h2>";
    echo "<p><a href='test.php'>اختبار النظام</a> | <a href='index.php'>الذهاب للموقع</a></p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>خطأ: " . $e->getMessage() . "</p>";
    echo "<h3>خطوات الحل:</h3>";
    echo "<ol>";
    echo "<li>تأكد من تشغيل XAMPP</li>";
    echo "<li>تأكد من تشغيل Apache و MySQL</li>";
    echo "<li>تأكد من إعدادات قاعدة البيانات في config.php</li>";
    echo "</ol>";
}
?>
