<?php
/**
 * هيدر لوحة الإدارة
 * Admin Panel Header
 */

// التأكد من تحميل ملف التحقق من الصلاحيات
if (!function_exists('isAdminLoggedIn')) {
    require_once 'auth.php';
}

// الحصول على المدير الحالي
$currentAdmin = getCurrentAdmin();

// الحصول على الإشعارات
$db = Database::getInstance();
$notifications = [
    'pending_ads' => $db->count('ads', ['status = ?'], ['pending']),
    'pending_reports' => $db->count('reports', ['status = ?'], ['pending']),
    'new_users_today' => $db->count('users', ['DATE(created_at) = ?'], [date('Y-m-d')]),
    'unread_messages' => $db->count('messages', ['is_read = ?'], [0])
];

$totalNotifications = array_sum($notifications);
?>

<header class="admin-header">
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <!-- Brand -->
            <a class="navbar-brand" href="<?php echo adminUrl(); ?>">
                <i class="fas fa-shield-alt me-2"></i>
                لوحة الإدارة
            </a>
            
            <!-- Toggle Sidebar Button (Mobile) -->
            <button class="btn btn-outline-light d-lg-none" type="button" id="sidebarToggle">
                <i class="fas fa-bars"></i>
            </button>
            
            <!-- Navigation Items -->
            <div class="navbar-nav ms-auto">
                <!-- Quick Actions Dropdown -->
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-plus-circle me-1"></i>
                        إضافة جديد
                    </a>
                    <ul class="dropdown-menu">
                        <li>
                            <a class="dropdown-item" href="<?php echo adminUrl('ads.php?action=add'); ?>">
                                <i class="fas fa-bullhorn me-2"></i>
                                إعلان جديد
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="<?php echo adminUrl('users.php?action=add'); ?>">
                                <i class="fas fa-user-plus me-2"></i>
                                مستخدم جديد
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="<?php echo adminUrl('categories.php?action=add'); ?>">
                                <i class="fas fa-folder-plus me-2"></i>
                                قسم جديد
                            </a>
                        </li>
                        <?php if (hasAdminPermission('manage_admins')): ?>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item" href="<?php echo adminUrl('admins.php?action=add'); ?>">
                                <i class="fas fa-user-shield me-2"></i>
                                مدير جديد
                            </a>
                        </li>
                        <?php endif; ?>
                    </ul>
                </div>
                
                <!-- Notifications Dropdown -->
                <div class="nav-item dropdown">
                    <a class="nav-link position-relative" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-bell"></i>
                        <?php if ($totalNotifications > 0): ?>
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                <?php echo $totalNotifications > 99 ? '99+' : $totalNotifications; ?>
                            </span>
                        <?php endif; ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end" style="width: 300px;">
                        <li class="dropdown-header">
                            <strong>الإشعارات</strong>
                            <?php if ($totalNotifications > 0): ?>
                                <span class="badge bg-primary ms-2"><?php echo $totalNotifications; ?></span>
                            <?php endif; ?>
                        </li>
                        
                        <?php if ($notifications['pending_ads'] > 0): ?>
                        <li>
                            <a class="dropdown-item" href="<?php echo adminUrl('ads.php?status=pending'); ?>">
                                <div class="d-flex align-items-center">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-clock text-warning"></i>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <div class="fw-bold">إعلانات معلقة</div>
                                        <small class="text-muted">
                                            <?php echo $notifications['pending_ads']; ?> إعلان في انتظار المراجعة
                                        </small>
                                    </div>
                                </div>
                            </a>
                        </li>
                        <?php endif; ?>
                        
                        <?php if ($notifications['pending_reports'] > 0): ?>
                        <li>
                            <a class="dropdown-item" href="<?php echo adminUrl('reports.php?status=pending'); ?>">
                                <div class="d-flex align-items-center">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-flag text-danger"></i>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <div class="fw-bold">تقارير جديدة</div>
                                        <small class="text-muted">
                                            <?php echo $notifications['pending_reports']; ?> تقرير يحتاج مراجعة
                                        </small>
                                    </div>
                                </div>
                            </a>
                        </li>
                        <?php endif; ?>
                        
                        <?php if ($notifications['new_users_today'] > 0): ?>
                        <li>
                            <a class="dropdown-item" href="<?php echo adminUrl('users.php?filter=today'); ?>">
                                <div class="d-flex align-items-center">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-user-plus text-success"></i>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <div class="fw-bold">مستخدمون جدد</div>
                                        <small class="text-muted">
                                            <?php echo $notifications['new_users_today']; ?> مستخدم انضم اليوم
                                        </small>
                                    </div>
                                </div>
                            </a>
                        </li>
                        <?php endif; ?>
                        
                        <?php if ($totalNotifications == 0): ?>
                        <li class="dropdown-item text-center py-3">
                            <i class="fas fa-check-circle text-success fa-2x mb-2"></i>
                            <div class="text-muted">لا توجد إشعارات جديدة</div>
                        </li>
                        <?php else: ?>
                        <li><hr class="dropdown-divider"></li>
                        <li class="dropdown-item text-center">
                            <a href="<?php echo adminUrl('notifications.php'); ?>" class="btn btn-sm btn-primary">
                                عرض جميع الإشعارات
                            </a>
                        </li>
                        <?php endif; ?>
                    </ul>
                </div>
                
                <!-- Quick Search -->
                <div class="nav-item">
                    <form class="d-flex" action="<?php echo adminUrl('search.php'); ?>" method="GET">
                        <div class="input-group">
                            <input class="form-control form-control-sm" type="search" name="q" 
                                   placeholder="بحث سريع..." style="width: 200px;">
                            <button class="btn btn-outline-light btn-sm" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                </div>
                
                <!-- Admin Profile Dropdown -->
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" role="button" data-bs-toggle="dropdown">
                        <?php if (!empty($currentAdmin['avatar'])): ?>
                            <img src="<?php echo upload_url($currentAdmin['avatar']); ?>" 
                                 alt="صورة المدير" class="admin-avatar me-2">
                        <?php else: ?>
                            <div class="admin-avatar me-2 bg-secondary d-flex align-items-center justify-content-center">
                                <i class="fas fa-user text-white"></i>
                            </div>
                        <?php endif; ?>
                        <span class="d-none d-md-inline">
                            <?php echo htmlspecialchars($_SESSION['admin_name']); ?>
                        </span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li class="dropdown-header">
                            <strong><?php echo htmlspecialchars($_SESSION['admin_name']); ?></strong>
                            <br>
                            <small class="text-muted">
                                <?php 
                                $roles = getAvailableRoles();
                                echo $roles[$_SESSION['admin_role']] ?? $_SESSION['admin_role'];
                                ?>
                            </small>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        
                        <li>
                            <a class="dropdown-item" href="<?php echo adminUrl('profile.php'); ?>">
                                <i class="fas fa-user me-2"></i>
                                الملف الشخصي
                            </a>
                        </li>
                        
                        <li>
                            <a class="dropdown-item" href="<?php echo adminUrl('settings.php'); ?>">
                                <i class="fas fa-cog me-2"></i>
                                الإعدادات
                            </a>
                        </li>
                        
                        <li>
                            <a class="dropdown-item" href="<?php echo adminUrl('activity-log.php'); ?>">
                                <i class="fas fa-history me-2"></i>
                                سجل الأنشطة
                            </a>
                        </li>
                        
                        <li><hr class="dropdown-divider"></li>
                        
                        <li>
                            <a class="dropdown-item" href="<?php echo url(); ?>" target="_blank">
                                <i class="fas fa-external-link-alt me-2"></i>
                                زيارة الموقع
                            </a>
                        </li>
                        
                        <li>
                            <a class="dropdown-item text-danger" href="<?php echo adminUrl('logout.php'); ?>">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                تسجيل الخروج
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>
</header>

<!-- Breadcrumb -->
<?php if (isset($adminBreadcrumb) && !empty($adminBreadcrumb)): ?>
<nav aria-label="breadcrumb" class="admin-breadcrumb">
    <div class="container-fluid">
        <ol class="breadcrumb mb-0 py-2">
            <li class="breadcrumb-item">
                <a href="<?php echo adminUrl(); ?>">
                    <i class="fas fa-home me-1"></i>
                    لوحة التحكم
                </a>
            </li>
            <?php foreach ($adminBreadcrumb as $item): ?>
                <?php if (isset($item['url'])): ?>
                    <li class="breadcrumb-item">
                        <a href="<?php echo $item['url']; ?>"><?php echo $item['title']; ?></a>
                    </li>
                <?php else: ?>
                    <li class="breadcrumb-item active" aria-current="page">
                        <?php echo $item['title']; ?>
                    </li>
                <?php endif; ?>
            <?php endforeach; ?>
        </ol>
    </div>
</nav>
<?php endif; ?>

<style>
/* تحسينات إضافية للهيدر */
.admin-header {
    backdrop-filter: blur(10px);
}

.admin-header .navbar-nav .nav-link {
    position: relative;
    overflow: hidden;
}

.admin-header .navbar-nav .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.admin-header .navbar-nav .nav-link:hover::before {
    left: 100%;
}

.admin-breadcrumb {
    background: rgba(255,255,255,0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid #e9ecef;
    margin-right: var(--admin-sidebar-width);
    transition: var(--admin-transition);
}

.admin-sidebar.collapsed ~ .admin-content .admin-breadcrumb {
    margin-right: 70px;
}

.dropdown-menu {
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
    border-radius: 10px;
    padding: 10px 0;
}

.dropdown-item {
    padding: 10px 20px;
    transition: var(--admin-transition);
}

.dropdown-item:hover {
    background: #f8f9fa;
    transform: translateX(5px);
}

.dropdown-header {
    padding: 15px 20px 10px;
    font-size: 0.9rem;
}

.admin-avatar {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid rgba(255,255,255,0.3);
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .admin-breadcrumb {
        margin-right: 0;
    }
    
    .admin-header .navbar-nav .nav-item:not(:last-child) {
        display: none;
    }
    
    .dropdown-menu {
        position: fixed !important;
        top: var(--admin-header-height) !important;
        left: 10px !important;
        right: 10px !important;
        width: auto !important;
        transform: none !important;
    }
}

/* تحسين إمكانية الوصول */
.admin-header .nav-link:focus {
    outline: 2px solid rgba(255,255,255,0.5);
    outline-offset: -2px;
}

.dropdown-item:focus {
    outline: 2px solid var(--admin-accent);
    outline-offset: -2px;
}

/* تأثيرات الحركة */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.dropdown-menu.show {
    animation: slideIn 0.3s ease;
}

/* تحسين الطباعة */
@media print {
    .admin-header,
    .admin-breadcrumb {
        display: none !important;
    }
}
</style>

<script>
// وظائف الهيدر
document.addEventListener('DOMContentLoaded', function() {
    // تبديل السايدبار في الشاشات الصغيرة
    const sidebarToggle = document.getElementById('sidebarToggle');
    const sidebar = document.querySelector('.admin-sidebar');
    
    if (sidebarToggle && sidebar) {
        sidebarToggle.addEventListener('click', function() {
            sidebar.classList.toggle('show');
        });
        
        // إغلاق السايدبار عند النقر خارجه
        document.addEventListener('click', function(e) {
            if (!sidebar.contains(e.target) && !sidebarToggle.contains(e.target)) {
                sidebar.classList.remove('show');
            }
        });
    }
    
    // تحديث الإشعارات كل دقيقة
    setInterval(function() {
        fetch('<?php echo adminUrl("ajax/notifications.php"); ?>')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateNotificationBadge(data.total);
                }
            })
            .catch(error => console.log('خطأ في تحديث الإشعارات:', error));
    }, 60000);
    
    // دالة تحديث شارة الإشعارات
    function updateNotificationBadge(count) {
        const badge = document.querySelector('.admin-header .badge');
        if (badge) {
            if (count > 0) {
                badge.textContent = count > 99 ? '99+' : count;
                badge.style.display = 'inline';
            } else {
                badge.style.display = 'none';
            }
        }
    }
    
    // البحث السريع
    const searchForm = document.querySelector('.admin-header form');
    if (searchForm) {
        const searchInput = searchForm.querySelector('input[name="q"]');
        
        // البحث التلقائي
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            const query = this.value.trim();
            
            if (query.length >= 2) {
                searchTimeout = setTimeout(() => {
                    showQuickSearchResults(query);
                }, 500);
            } else {
                hideQuickSearchResults();
            }
        });
    }
    
    // دالة عرض نتائج البحث السريع
    function showQuickSearchResults(query) {
        // يمكن تنفيذ هذه الوظيفة لاحقاً
        console.log('البحث عن:', query);
    }
    
    // دالة إخفاء نتائج البحث السريع
    function hideQuickSearchResults() {
        // يمكن تنفيذ هذه الوظيفة لاحقاً
    }
});
</script>
