<?php
/**
 * ملف الهيدر المشترك
 * Shared Header File
 */

// التأكد من تحميل الملفات المطلوبة
if (!defined('SITE_ROOT')) {
    require_once 'config.php';
    require_once 'functions.php';
}

// الحصول على الأقسام للقائمة
$headerCategories = getCategoryTree();

// الحصول على عدد الرسائل غير المقروءة للمستخدم المسجل
$unreadMessages = 0;
if (isLoggedIn()) {
    $db = Database::getInstance();
    $unreadMessages = $db->count('messages', 
        ['receiver_id = ? AND is_read = ?'], 
        [$_SESSION['user_id'], 0]
    );
}
?>

<header class="header">
    <!-- Top Bar -->
    <div class="top-bar bg-dark text-light py-2 d-none d-md-block">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="contact-info">
                        <span class="me-3">
                            <i class="fas fa-envelope me-1"></i>
                            <?php echo getSetting('admin_email', '<EMAIL>'); ?>
                        </span>
                        <span>
                            <i class="fas fa-phone me-1"></i>
                            +966 50 123 4567
                        </span>
                    </div>
                </div>
                <div class="col-md-6 text-end">
                    <div class="social-links">
                        <a href="#" class="text-light me-2" title="فيسبوك">
                            <i class="fab fa-facebook"></i>
                        </a>
                        <a href="#" class="text-light me-2" title="تويتر">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="text-light me-2" title="إنستغرام">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <a href="#" class="text-light" title="لينكد إن">
                            <i class="fab fa-linkedin"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <!-- Brand -->
            <a class="navbar-brand" href="<?php echo url(); ?>">
                <i class="fas fa-bullhorn me-2"></i>
                <?php echo getSetting('site_name', 'موقع الإعلانات'); ?>
            </a>
            
            <!-- Mobile Toggle -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <!-- Navigation Menu -->
            <div class="collapse navbar-collapse" id="navbarNav">
                <!-- Main Menu -->
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'index.php' ? 'active' : ''; ?>" 
                           href="<?php echo url(); ?>">
                            <i class="fas fa-home me-1"></i>
                            الرئيسية
                        </a>
                    </li>
                    
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'ads.php' ? 'active' : ''; ?>" 
                           href="<?php echo url('ads.php'); ?>">
                            <i class="fas fa-list me-1"></i>
                            جميع الإعلانات
                        </a>
                    </li>
                    
                    <!-- Categories Dropdown -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-th-large me-1"></i>
                            الأقسام
                        </a>
                        <ul class="dropdown-menu">
                            <?php foreach ($headerCategories as $category): ?>
                                <li>
                                    <a class="dropdown-item" href="<?php echo url('category.php?id=' . $category['id']); ?>">
                                        <i class="<?php echo $category['icon']; ?> me-2"></i>
                                        <?php echo $category['name_ar']; ?>
                                        <?php if (!empty($category['children'])): ?>
                                            <i class="fas fa-chevron-left float-end mt-1"></i>
                                        <?php endif; ?>
                                    </a>
                                    
                                    <?php if (!empty($category['children'])): ?>
                                        <ul class="dropdown-submenu">
                                            <?php foreach ($category['children'] as $child): ?>
                                                <li>
                                                    <a class="dropdown-item" href="<?php echo url('category.php?id=' . $child['id']); ?>">
                                                        <?php echo $child['name_ar']; ?>
                                                    </a>
                                                </li>
                                            <?php endforeach; ?>
                                        </ul>
                                    <?php endif; ?>
                                </li>
                            <?php endforeach; ?>
                            
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="<?php echo url('ads.php'); ?>">
                                    <i class="fas fa-eye me-2"></i>
                                    عرض جميع الأقسام
                                </a>
                            </li>
                        </ul>
                    </li>
                    
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo url('contact.php'); ?>">
                            <i class="fas fa-envelope me-1"></i>
                            اتصل بنا
                        </a>
                    </li>
                </ul>
                
                <!-- Search Form (Mobile) -->
                <form class="d-flex d-lg-none mb-3" action="<?php echo url('search.php'); ?>" method="GET">
                    <input class="form-control me-2" type="search" name="q" placeholder="ابحث..." aria-label="البحث">
                    <button class="btn btn-outline-light" type="submit">
                        <i class="fas fa-search"></i>
                    </button>
                </form>
                
                <!-- User Menu -->
                <ul class="navbar-nav">
                    <?php if (isLoggedIn()): ?>
                        <!-- Messages -->
                        <li class="nav-item dropdown">
                            <a class="nav-link position-relative" href="<?php echo url('messages.php'); ?>">
                                <i class="fas fa-envelope"></i>
                                <?php if ($unreadMessages > 0): ?>
                                    <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                        <?php echo $unreadMessages > 99 ? '99+' : $unreadMessages; ?>
                                    </span>
                                <?php endif; ?>
                            </a>
                        </li>
                        
                        <!-- User Dropdown -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-1"></i>
                                <?php echo htmlspecialchars($_SESSION['user_name']); ?>
                            </a>
                            <ul class="dropdown-menu">
                                <li>
                                    <a class="dropdown-item" href="<?php echo url('profile.php'); ?>">
                                        <i class="fas fa-user me-2"></i>
                                        حسابي
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="<?php echo url('my-ads.php'); ?>">
                                        <i class="fas fa-list me-2"></i>
                                        إعلاناتي
                                        <?php
                                        $myAdsCount = $db->count('ads', ['user_id = ? AND status != ?'], [$_SESSION['user_id'], 'deleted']);
                                        if ($myAdsCount > 0):
                                        ?>
                                            <span class="badge bg-primary ms-1"><?php echo $myAdsCount; ?></span>
                                        <?php endif; ?>
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="<?php echo url('favorites.php'); ?>">
                                        <i class="fas fa-heart me-2"></i>
                                        المفضلة
                                        <?php
                                        $favoritesCount = $db->count('favorites', ['user_id = ?'], [$_SESSION['user_id']]);
                                        if ($favoritesCount > 0):
                                        ?>
                                            <span class="badge bg-danger ms-1"><?php echo $favoritesCount; ?></span>
                                        <?php endif; ?>
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="<?php echo url('messages.php'); ?>">
                                        <i class="fas fa-envelope me-2"></i>
                                        الرسائل
                                        <?php if ($unreadMessages > 0): ?>
                                            <span class="badge bg-warning ms-1"><?php echo $unreadMessages; ?></span>
                                        <?php endif; ?>
                                    </a>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <a class="dropdown-item" href="<?php echo url('settings.php'); ?>">
                                        <i class="fas fa-cog me-2"></i>
                                        الإعدادات
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="<?php echo url('logout.php'); ?>">
                                        <i class="fas fa-sign-out-alt me-2"></i>
                                        تسجيل الخروج
                                    </a>
                                </li>
                            </ul>
                        </li>
                        
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo url('login.php'); ?>">
                                <i class="fas fa-sign-in-alt me-1"></i>
                                تسجيل الدخول
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo url('register.php'); ?>">
                                <i class="fas fa-user-plus me-1"></i>
                                إنشاء حساب
                            </a>
                        </li>
                    <?php endif; ?>
                    
                    <!-- Add Ad Button -->
                    <li class="nav-item">
                        <a class="btn btn-warning ms-2" href="<?php echo url('add-ad.php'); ?>">
                            <i class="fas fa-plus me-1"></i>
                            إضافة إعلان
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    
    <!-- Search Bar (Desktop) -->
    <div class="search-bar bg-light py-3 d-none d-lg-block">
        <div class="container">
            <form action="<?php echo url('search.php'); ?>" method="GET" class="search-form">
                <div class="input-group input-group-lg">
                    <input type="text" class="form-control" name="q" 
                           placeholder="ابحث عن إعلان..." 
                           value="<?php echo htmlspecialchars($_GET['q'] ?? ''); ?>">
                    
                    <select class="form-select" name="category" style="max-width: 200px;">
                        <option value="">جميع الأقسام</option>
                        <?php foreach ($headerCategories as $category): ?>
                            <option value="<?php echo $category['id']; ?>" 
                                    <?php echo ($_GET['category'] ?? '') == $category['id'] ? 'selected' : ''; ?>>
                                <?php echo $category['name_ar']; ?>
                            </option>
                            <?php if (!empty($category['children'])): ?>
                                <?php foreach ($category['children'] as $child): ?>
                                    <option value="<?php echo $child['id']; ?>" 
                                            <?php echo ($_GET['category'] ?? '') == $child['id'] ? 'selected' : ''; ?>>
                                        &nbsp;&nbsp;- <?php echo $child['name_ar']; ?>
                                    </option>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </select>
                    
                    <input type="text" class="form-control" name="location" 
                           placeholder="الموقع..." 
                           value="<?php echo htmlspecialchars($_GET['location'] ?? ''); ?>"
                           style="max-width: 150px;">
                    
                    <button class="btn btn-primary" type="submit">
                        <i class="fas fa-search me-1"></i>
                        بحث
                    </button>
                </div>
            </form>
        </div>
    </div>
</header>

<!-- Breadcrumb -->
<?php if (isset($breadcrumb) && !empty($breadcrumb)): ?>
<nav aria-label="breadcrumb" class="bg-light">
    <div class="container">
        <ol class="breadcrumb py-3 mb-0">
            <li class="breadcrumb-item">
                <a href="<?php echo url(); ?>">الرئيسية</a>
            </li>
            <?php foreach ($breadcrumb as $item): ?>
                <?php if (isset($item['url'])): ?>
                    <li class="breadcrumb-item">
                        <a href="<?php echo $item['url']; ?>"><?php echo $item['title']; ?></a>
                    </li>
                <?php else: ?>
                    <li class="breadcrumb-item active" aria-current="page">
                        <?php echo $item['title']; ?>
                    </li>
                <?php endif; ?>
            <?php endforeach; ?>
        </ol>
    </div>
</nav>
<?php endif; ?>

<!-- Session Messages -->
<div class="container mt-3">
    <?php echo showSessionMessages(); ?>
</div>

<style>
/* تحسينات إضافية للهيدر */
.top-bar {
    font-size: 0.9rem;
}

.top-bar .social-links a {
    transition: color 0.3s ease;
}

.top-bar .social-links a:hover {
    color: #007bff !important;
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
}

.navbar-nav .nav-link:hover {
    transform: translateY(-1px);
}

.navbar-nav .nav-link.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 2px;
    background: #fff;
    border-radius: 1px;
}

.dropdown-submenu {
    position: absolute;
    top: 0;
    right: 100%;
    margin-top: -1px;
    background: white;
    border: 1px solid rgba(0,0,0,.15);
    border-radius: 0.375rem;
    box-shadow: 0 0.5rem 1rem rgba(0,0,0,.175);
    display: none;
    min-width: 200px;
}

.dropdown-item:hover .dropdown-submenu {
    display: block;
}

.search-bar .input-group {
    max-width: 800px;
    margin: 0 auto;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-radius: 8px;
    overflow: hidden;
}

.search-bar .form-control,
.search-bar .form-select {
    border: none;
    padding: 15px;
}

.search-bar .btn {
    padding: 15px 25px;
    border: none;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 991px) {
    .navbar-nav .nav-link {
        padding: 0.75rem 1rem;
    }
    
    .dropdown-submenu {
        position: static;
        display: block;
        margin-right: 1rem;
        border: none;
        box-shadow: none;
        background: transparent;
    }
    
    .dropdown-submenu .dropdown-item {
        padding-right: 2rem;
        font-size: 0.9rem;
    }
}

/* تحسين إمكانية الوصول */
.navbar-nav .nav-link:focus {
    outline: 2px solid #fff;
    outline-offset: 2px;
}

.dropdown-item:focus {
    outline: 2px solid #007bff;
    outline-offset: -2px;
}
</style>
