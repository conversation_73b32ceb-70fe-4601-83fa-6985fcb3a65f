<?php
// ملف اختبار بسيط
echo "PHP يعمل بشكل صحيح!<br>";
echo "إصدار PHP: " . phpversion() . "<br>";
echo "الوقت الحالي: " . date('Y-m-d H:i:s') . "<br>";

// اختبار الاتصال بقاعدة البيانات
try {
    $pdo = new PDO('mysql:host=localhost;dbname=classified_ads;charset=utf8mb4', 'root', '');
    echo "الاتصال بقاعدة البيانات: ناجح<br>";
    
    // اختبار وجود الجداول
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    echo "عدد الجداول: " . count($tables) . "<br>";
    
    if (count($tables) > 0) {
        echo "الجداول الموجودة:<br>";
        foreach ($tables as $table) {
            echo "- " . $table . "<br>";
        }
    }
    
} catch (PDOException $e) {
    echo "خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "<br>";
}

// اختبار تحميل الملفات
echo "<br>اختبار تحميل الملفات:<br>";

if (file_exists('includes/config.php')) {
    echo "✓ ملف config.php موجود<br>";
} else {
    echo "✗ ملف config.php غير موجود<br>";
}

if (file_exists('includes/functions.php')) {
    echo "✓ ملف functions.php موجود<br>";
} else {
    echo "✗ ملف functions.php غير موجود<br>";
}

if (file_exists('includes/database.php')) {
    echo "✓ ملف database.php موجود<br>";
} else {
    echo "✗ ملف database.php غير موجود<br>";
}

if (file_exists('includes/models.php')) {
    echo "✓ ملف models.php موجود<br>";
} else {
    echo "✗ ملف models.php غير موجود<br>";
}

// اختبار الأخطاء
echo "<br>إعدادات الأخطاء:<br>";
echo "display_errors: " . ini_get('display_errors') . "<br>";
echo "error_reporting: " . error_reporting() . "<br>";
echo "log_errors: " . ini_get('log_errors') . "<br>";

phpinfo();
?>
