<?php
/**
 * صفحة تسجيل دخول لوحة الإدارة
 * Admin Login Page
 */

require_once 'includes/auth.php';

// إعادة توجيه المدير المسجل دخوله بالفعل
if (isAdminLoggedIn()) {
    $redirectUrl = $_SESSION['admin_redirect_url'] ?? adminUrl('index.php');
    unset($_SESSION['admin_redirect_url']);
    redirect($redirectUrl);
}

$error = '';
$loginAttempts = $_SESSION['admin_login_attempts'] ?? 0;
$lockoutTime = $_SESSION['admin_lockout_time'] ?? 0;

// التحقق من حالة القفل
if ($lockoutTime > time()) {
    $remainingTime = ceil(($lockoutTime - time()) / 60);
    $error = "تم قفل الحساب مؤقتاً. حاول مرة أخرى بعد $remainingTime دقيقة.";
}

// معالجة تسجيل الدخول
if ($_SERVER['REQUEST_METHOD'] === 'POST' && empty($error)) {
    $username = sanitize($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $remember = isset($_POST['remember']);
    
    // التحقق من رمز CSRF
    if (!verifyCSRFToken($_POST['_token'] ?? '')) {
        $error = 'رمز الأمان غير صحيح';
    } elseif (empty($username) || empty($password)) {
        $error = 'يرجى إدخال اسم المستخدم وكلمة المرور';
    } else {
        // محاولة تسجيل الدخول
        if (adminLogin($username, $password)) {
            // إعادة تعيين محاولات تسجيل الدخول
            unset($_SESSION['admin_login_attempts']);
            unset($_SESSION['admin_lockout_time']);
            
            // تعيين cookie للتذكر
            if ($remember) {
                $token = bin2hex(random_bytes(32));
                setcookie('admin_remember_token', $token, time() + (86400 * 30), '/admin/');
                // يمكن حفظ الرمز في قاعدة البيانات للتحقق لاحقاً
            }
            
            // إعادة التوجيه
            $redirectUrl = $_SESSION['admin_redirect_url'] ?? adminUrl('index.php');
            unset($_SESSION['admin_redirect_url']);
            redirect($redirectUrl);
        } else {
            $error = 'اسم المستخدم أو كلمة المرور غير صحيحة';
            
            // زيادة عداد المحاولات
            $loginAttempts++;
            $_SESSION['admin_login_attempts'] = $loginAttempts;
            
            // قفل الحساب بعد 5 محاولات فاشلة
            if ($loginAttempts >= 5) {
                $_SESSION['admin_lockout_time'] = time() + (15 * 60); // 15 دقيقة
                $error = 'تم تجاوز عدد المحاولات المسموحة. تم قفل الحساب لمدة 15 دقيقة.';
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - لوحة الإدارة</title>
    <meta name="robots" content="noindex, nofollow">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 400px;
            width: 100%;
            margin: 20px;
        }
        
        .login-header {
            background: linear-gradient(135deg, #2c3e50, #34495e);
            color: white;
            padding: 40px 30px 30px;
            text-align: center;
            position: relative;
        }
        
        .login-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }
        
        .login-header > * {
            position: relative;
            z-index: 1;
        }
        
        .login-icon {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 2rem;
        }
        
        .login-body {
            padding: 40px 30px;
        }
        
        .form-floating {
            margin-bottom: 20px;
        }
        
        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 15px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-login {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 10px;
            padding: 15px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
            width: 100%;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        .btn-login:disabled {
            opacity: 0.6;
            transform: none;
            box-shadow: none;
        }
        
        .alert {
            border-radius: 10px;
            border: none;
            margin-bottom: 20px;
        }
        
        .form-check {
            margin: 20px 0;
        }
        
        .form-check-input:checked {
            background-color: #667eea;
            border-color: #667eea;
        }
        
        .login-footer {
            text-align: center;
            padding: 20px 30px;
            background: #f8f9fa;
            border-top: 1px solid #e9ecef;
        }
        
        .login-footer a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }
        
        .login-footer a:hover {
            text-decoration: underline;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .security-info {
            background: rgba(255, 193, 7, 0.1);
            border: 1px solid rgba(255, 193, 7, 0.3);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            font-size: 0.9rem;
        }
        
        .attempts-warning {
            background: rgba(220, 53, 69, 0.1);
            border: 1px solid rgba(220, 53, 69, 0.3);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            color: #dc3545;
            font-size: 0.9rem;
        }
        
        @media (max-width: 576px) {
            .login-container {
                margin: 10px;
                border-radius: 15px;
            }
            
            .login-header {
                padding: 30px 20px 20px;
            }
            
            .login-body {
                padding: 30px 20px;
            }
            
            .login-icon {
                width: 60px;
                height: 60px;
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <div class="login-icon">
                <i class="fas fa-shield-alt"></i>
            </div>
            <h3 class="mb-0">لوحة الإدارة</h3>
            <p class="mb-0 mt-2 opacity-75">تسجيل الدخول للوحة التحكم</p>
        </div>
        
        <div class="login-body">
            <?php if (!empty($error)): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo $error; ?>
                </div>
            <?php endif; ?>
            
            <?php if ($loginAttempts > 0 && $loginAttempts < 5): ?>
                <div class="attempts-warning">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    محاولة فاشلة رقم <?php echo $loginAttempts; ?> من 5. 
                    <?php echo (5 - $loginAttempts); ?> محاولات متبقية.
                </div>
            <?php endif; ?>
            
            <div class="security-info">
                <i class="fas fa-info-circle me-2"></i>
                هذه منطقة محمية. يتم تسجيل جميع محاولات الدخول.
            </div>
            
            <form method="POST" id="loginForm">
                <input type="hidden" name="_token" value="<?php echo generateCSRFToken(); ?>">
                
                <div class="form-floating">
                    <input type="text" class="form-control" id="username" name="username" 
                           placeholder="اسم المستخدم" required 
                           value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>"
                           <?php echo ($lockoutTime > time()) ? 'disabled' : ''; ?>>
                    <label for="username">
                        <i class="fas fa-user me-2"></i>
                        اسم المستخدم
                    </label>
                </div>
                
                <div class="form-floating">
                    <input type="password" class="form-control" id="password" name="password" 
                           placeholder="كلمة المرور" required
                           <?php echo ($lockoutTime > time()) ? 'disabled' : ''; ?>>
                    <label for="password">
                        <i class="fas fa-lock me-2"></i>
                        كلمة المرور
                    </label>
                </div>
                
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="remember" name="remember">
                    <label class="form-check-label" for="remember">
                        تذكرني لمدة 30 يوماً
                    </label>
                </div>
                
                <button type="submit" class="btn btn-primary btn-login" 
                        <?php echo ($lockoutTime > time()) ? 'disabled' : ''; ?>>
                    <i class="fas fa-sign-in-alt me-2"></i>
                    تسجيل الدخول
                </button>
            </form>
        </div>
        
        <div class="login-footer">
            <small class="text-muted">
                <i class="fas fa-shield-alt me-1"></i>
                محمي بتشفير SSL
            </small>
            <br>
            <a href="<?php echo url(); ?>" class="mt-2 d-inline-block">
                <i class="fas fa-arrow-right me-1"></i>
                العودة للموقع الرئيسي
            </a>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const loginForm = document.getElementById('loginForm');
            const submitBtn = loginForm.querySelector('button[type="submit"]');
            
            // تركيز على حقل اسم المستخدم
            const usernameField = document.getElementById('username');
            if (!usernameField.disabled) {
                usernameField.focus();
            }
            
            // معالجة إرسال النموذج
            loginForm.addEventListener('submit', function(e) {
                const username = document.getElementById('username').value.trim();
                const password = document.getElementById('password').value;
                
                if (!username || !password) {
                    e.preventDefault();
                    alert('يرجى إدخال اسم المستخدم وكلمة المرور');
                    return;
                }
                
                // إظهار مؤشر التحميل
                submitBtn.disabled = true;
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<span class="loading"></span> جاري التحقق...';
                
                // إعادة تفعيل الزر بعد 10 ثوان في حالة عدم الاستجابة
                setTimeout(() => {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = originalText;
                }, 10000);
            });
            
            // منع النسخ واللصق في حقل كلمة المرور (أمان إضافي)
            document.getElementById('password').addEventListener('paste', function(e) {
                e.preventDefault();
                alert('لا يمكن لصق كلمة المرور لأسباب أمنية');
            });
            
            // تحديث عداد الوقت المتبقي للقفل
            <?php if ($lockoutTime > time()): ?>
            let remainingTime = <?php echo $lockoutTime - time(); ?>;
            const updateTimer = () => {
                if (remainingTime > 0) {
                    const minutes = Math.floor(remainingTime / 60);
                    const seconds = remainingTime % 60;
                    document.title = `قفل مؤقت - ${minutes}:${seconds.toString().padStart(2, '0')}`;
                    remainingTime--;
                    setTimeout(updateTimer, 1000);
                } else {
                    location.reload();
                }
            };
            updateTimer();
            <?php endif; ?>
            
            // حماية من محاولات البروت فورس
            let failedAttempts = <?php echo $loginAttempts; ?>;
            if (failedAttempts >= 3) {
                // إضافة تأخير إضافي
                setTimeout(() => {
                    submitBtn.disabled = false;
                }, 2000);
                submitBtn.disabled = true;
            }
        });
        
        // منع فتح أدوات المطور (حماية إضافية)
        document.addEventListener('keydown', function(e) {
            if (e.key === 'F12' || 
                (e.ctrlKey && e.shiftKey && e.key === 'I') ||
                (e.ctrlKey && e.shiftKey && e.key === 'C') ||
                (e.ctrlKey && e.key === 'U')) {
                e.preventDefault();
                return false;
            }
        });
        
        // منع النقر بالزر الأيمن
        document.addEventListener('contextmenu', function(e) {
            e.preventDefault();
            return false;
        });
    </script>
</body>
</html>
