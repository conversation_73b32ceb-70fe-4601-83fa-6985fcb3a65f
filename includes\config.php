<?php
/**
 * ملف التكوين الرئيسي لموقع الإعلانات
 * Main Configuration File for Classified Ads Website
 */

// منع الوصول المباشر
if (!defined('SITE_ROOT')) {
    define('SITE_ROOT', dirname(__DIR__));
}

// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'classified_ads');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// إعدادات الموقع
define('SITE_URL', 'http://localhost/api');
define('SITE_NAME', 'موقع الإعلانات');
define('SITE_DESCRIPTION', 'موقع إعلانات مبوبة شامل');
define('ADMIN_EMAIL', '<EMAIL>');

// إعدادات الأمان
define('HASH_ALGO', PASSWORD_DEFAULT);
define('SESSION_LIFETIME', 3600 * 24 * 7); // أسبوع
define('CSRF_TOKEN_NAME', '_token');
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 دقيقة

// إعدادات الملفات
define('UPLOAD_PATH', SITE_ROOT . '/assets/uploads/');
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'webp']);
define('MAX_IMAGES_PER_AD', 5);

// إعدادات الإعلانات
define('ADS_PER_PAGE', 12);
define('AD_EXPIRY_DAYS', 30);
define('FEATURED_AD_PRICE', 10.00);
define('URGENT_AD_PRICE', 5.00);

// إعدادات البريد الإلكتروني
define('SMTP_HOST', 'localhost');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '');
define('SMTP_PASSWORD', '');
define('SMTP_ENCRYPTION', 'tls');

// إعدادات اللغة والمنطقة الزمنية
define('DEFAULT_LANGUAGE', 'ar');
define('DEFAULT_TIMEZONE', 'Asia/Riyadh');
define('DATE_FORMAT', 'Y-m-d H:i:s');
define('DISPLAY_DATE_FORMAT', 'd/m/Y');

// إعدادات التطوير
define('DEBUG_MODE', true);
define('LOG_ERRORS', true);
define('ERROR_LOG_FILE', SITE_ROOT . '/logs/error.log');

// تعيين المنطقة الزمنية
date_default_timezone_set(DEFAULT_TIMEZONE);

// بدء الجلسة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// تعيين ترميز الأحرف
mb_internal_encoding('UTF-8');
mb_http_output('UTF-8');

// إعدادات PHP
ini_set('display_errors', DEBUG_MODE ? 1 : 0);
ini_set('log_errors', LOG_ERRORS ? 1 : 0);
if (LOG_ERRORS && defined('ERROR_LOG_FILE')) {
    ini_set('error_log', ERROR_LOG_FILE);
}

// إعدادات الجلسة
ini_set('session.cookie_httponly', 1);
ini_set('session.use_only_cookies', 1);
ini_set('session.cookie_secure', isset($_SERVER['HTTPS']) ? 1 : 0);
ini_set('session.gc_maxlifetime', SESSION_LIFETIME);

// دالة للحصول على إعدادات الموقع من قاعدة البيانات
function getSiteSettings() {
    static $settings = null;
    
    if ($settings === null) {
        try {
            $pdo = new PDO(
                "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET,
                DB_USER,
                DB_PASS,
                [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false
                ]
            );
            
            $stmt = $pdo->query("SELECT setting_key, setting_value FROM site_settings");
            $settings = [];
            while ($row = $stmt->fetch()) {
                $settings[$row['setting_key']] = $row['setting_value'];
            }
        } catch (PDOException $e) {
            $settings = [];
            if (DEBUG_MODE) {
                error_log("Database connection error: " . $e->getMessage());
            }
        }
    }
    
    return $settings;
}

// دالة للحصول على قيمة إعداد معين
function getSetting($key, $default = null) {
    $settings = getSiteSettings();
    return isset($settings[$key]) ? $settings[$key] : $default;
}

// دالة لإنشاء URL آمن
function url($path = '') {
    return rtrim(SITE_URL, '/') . '/' . ltrim($path, '/');
}

// دالة لإنشاء URL للأصول
function asset($path = '') {
    return url('assets/' . ltrim($path, '/'));
}

// دالة لإنشاء URL للرفع
function upload_url($path = '') {
    return asset('uploads/' . ltrim($path, '/'));
}

// دالة للتحقق من وجود HTTPS
function isSecure() {
    return (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off')
        || $_SERVER['SERVER_PORT'] == 443;
}

// دالة لإعادة التوجيه
function redirect($url, $statusCode = 302) {
    if (!headers_sent()) {
        header('Location: ' . $url, true, $statusCode);
        exit();
    }
}

// دالة لإنشاء رمز CSRF
function generateCSRFToken() {
    if (!isset($_SESSION[CSRF_TOKEN_NAME])) {
        $_SESSION[CSRF_TOKEN_NAME] = bin2hex(random_bytes(32));
    }
    return $_SESSION[CSRF_TOKEN_NAME];
}

// دالة للتحقق من رمز CSRF
function verifyCSRFToken($token) {
    return isset($_SESSION[CSRF_TOKEN_NAME]) && hash_equals($_SESSION[CSRF_TOKEN_NAME], $token);
}

// دالة لتنظيف البيانات
function sanitize($data) {
    if (is_array($data)) {
        return array_map('sanitize', $data);
    }
    return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
}

// دالة للتحقق من صحة البريد الإلكتروني
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

// دالة لتوليد كلمة مرور عشوائية
function generateRandomPassword($length = 8) {
    $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    return substr(str_shuffle($chars), 0, $length);
}

// دالة لتشفير كلمة المرور
function hashPassword($password) {
    return password_hash($password, HASH_ALGO);
}

// دالة للتحقق من كلمة المرور
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

// دالة لإنشاء slug من النص العربي
function createSlug($text) {
    // تحويل النص العربي إلى transliteration
    $transliteration = [
        'ا' => 'a', 'ب' => 'b', 'ت' => 't', 'ث' => 'th', 'ج' => 'j',
        'ح' => 'h', 'خ' => 'kh', 'د' => 'd', 'ذ' => 'th', 'ر' => 'r',
        'ز' => 'z', 'س' => 's', 'ش' => 'sh', 'ص' => 's', 'ض' => 'd',
        'ط' => 't', 'ظ' => 'z', 'ع' => 'a', 'غ' => 'gh', 'ف' => 'f',
        'ق' => 'q', 'ك' => 'k', 'ل' => 'l', 'م' => 'm', 'ن' => 'n',
        'ه' => 'h', 'و' => 'w', 'ي' => 'y', 'ة' => 'h', 'ى' => 'a'
    ];
    
    $text = strtr($text, $transliteration);
    $text = strtolower($text);
    $text = preg_replace('/[^a-z0-9\s-]/', '', $text);
    $text = preg_replace('/[\s-]+/', '-', $text);
    return trim($text, '-');
}

// دالة لتنسيق التاريخ
function formatDate($date, $format = null) {
    if ($format === null) {
        $format = DISPLAY_DATE_FORMAT;
    }
    return date($format, strtotime($date));
}

// دالة لحساب الوقت المنقضي
function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'الآن';
    if ($time < 3600) return floor($time/60) . ' دقيقة';
    if ($time < 86400) return floor($time/3600) . ' ساعة';
    if ($time < 2592000) return floor($time/86400) . ' يوم';
    if ($time < 31536000) return floor($time/2592000) . ' شهر';
    return floor($time/31536000) . ' سنة';
}

// دالة لتنسيق الأرقام
function formatNumber($number, $decimals = 0) {
    return number_format($number, $decimals, '.', ',');
}

// دالة لتنسيق السعر
function formatPrice($price) {
    if ($price == 0) return 'مجاني';
    return formatNumber($price, 2) . ' ريال';
}

// دالة للحصول على عنوان IP الحقيقي
function getRealIP() {
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        return $_SERVER['HTTP_CLIENT_IP'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        return $_SERVER['HTTP_X_FORWARDED_FOR'];
    } else {
        return $_SERVER['REMOTE_ADDR'];
    }
}

// دالة لتسجيل الأخطاء
function logError($message, $file = null, $line = null) {
    $log = date('[Y-m-d H:i:s] ') . $message;
    if ($file) $log .= " in $file";
    if ($line) $log .= " on line $line";
    $log .= PHP_EOL;
    
    if (LOG_ERRORS && defined('ERROR_LOG_FILE')) {
        error_log($log, 3, ERROR_LOG_FILE);
    }
}

// معالج الأخطاء المخصص
function customErrorHandler($errno, $errstr, $errfile, $errline) {
    if (!(error_reporting() & $errno)) {
        return false;
    }
    
    logError("Error [$errno]: $errstr", $errfile, $errline);
    
    if (DEBUG_MODE) {
        echo "<b>Error:</b> [$errno] $errstr in $errfile on line $errline<br>";
    }
    
    return true;
}

// تعيين معالج الأخطاء
set_error_handler('customErrorHandler');

// إنشاء مجلدات الرفع إذا لم تكن موجودة
$uploadDirs = [
    UPLOAD_PATH,
    UPLOAD_PATH . 'ads/',
    UPLOAD_PATH . 'users/',
    UPLOAD_PATH . 'temp/',
    SITE_ROOT . '/logs/'
];

foreach ($uploadDirs as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}

?>
