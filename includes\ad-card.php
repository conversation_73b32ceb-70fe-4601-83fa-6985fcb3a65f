<?php
/**
 * بطاقة الإعلان - مكون قابل لإعادة الاستخدام
 * Ad Card Component - Reusable
 */

// التأكد من وجود بيانات الإعلان
if (!isset($ad) || empty($ad)) {
    return;
}

// الحصول على الصورة الرئيسية
$primaryImage = null;
if (!empty($ad['images'])) {
    if (is_string($ad['images'])) {
        $images = json_decode($ad['images'], true);
        $primaryImage = !empty($images) ? $images[0] : null;
    } else {
        $primaryImage = $ad['images'][0] ?? null;
    }
}

// إعداد الصورة الافتراضية
$imageUrl = $primaryImage ? upload_url($primaryImage) : asset('images/no-image.jpg');

// تحديد حالة المفضلة
$isFavorite = false;
if (isLoggedIn()) {
    $db = Database::getInstance();
    $isFavorite = $db->exists('favorites', 
        ['user_id = ? AND ad_id = ?'], 
        [$_SESSION['user_id'], $ad['id']]
    );
}

// تنسيق السعر
$priceText = 'غير محدد';
if ($ad['price'] > 0) {
    $priceText = formatPrice($ad['price']);
    if ($ad['price_type'] === 'negotiable') {
        $priceText .= ' (قابل للتفاوض)';
    }
} elseif ($ad['price'] == 0) {
    $priceText = 'مجاني';
}

// حساب الوقت المنقضي
$timeAgo = timeAgo($ad['created_at']);
?>

<div class="ad-card">
    <div class="ad-card-image">
        <img src="<?php echo $imageUrl; ?>" alt="<?php echo htmlspecialchars($ad['title']); ?>" loading="lazy">
        
        <!-- شارات الإعلان -->
        <div class="ad-card-badges">
            <?php if ($ad['featured']): ?>
                <span class="badge bg-warning text-dark">
                    <i class="fas fa-star me-1"></i>مميز
                </span>
            <?php endif; ?>
            
            <?php if ($ad['urgent']): ?>
                <span class="badge bg-danger">
                    <i class="fas fa-exclamation me-1"></i>عاجل
                </span>
            <?php endif; ?>
            
            <?php if ($ad['price'] == 0): ?>
                <span class="badge bg-success">مجاني</span>
            <?php endif; ?>
        </div>
        
        <!-- زر المفضلة -->
        <?php if (isLoggedIn()): ?>
            <button class="ad-card-favorite favorite-btn <?php echo $isFavorite ? 'active' : ''; ?>" 
                    data-ad-id="<?php echo $ad['id']; ?>"
                    title="<?php echo $isFavorite ? 'إزالة من المفضلة' : 'إضافة للمفضلة'; ?>">
                <i class="<?php echo $isFavorite ? 'fas' : 'far'; ?> fa-heart"></i>
            </button>
        <?php endif; ?>
    </div>
    
    <div class="ad-card-body">
        <h5 class="ad-card-title">
            <a href="<?php echo url('ad-details.php?id=' . $ad['id']); ?>">
                <?php echo htmlspecialchars($ad['title']); ?>
            </a>
        </h5>
        
        <p class="ad-card-description">
            <?php echo htmlspecialchars(truncateText($ad['description'], 100)); ?>
        </p>
        
        <div class="ad-card-price">
            <?php echo $priceText; ?>
        </div>
        
        <div class="ad-card-meta">
            <div class="ad-card-location">
                <i class="fas fa-map-marker-alt"></i>
                <?php echo htmlspecialchars($ad['location']); ?>
            </div>
            
            <div class="ad-card-date">
                <i class="fas fa-clock"></i>
                <?php echo $timeAgo; ?>
            </div>
        </div>
        
        <!-- معلومات إضافية -->
        <div class="ad-card-stats mt-2">
            <small class="text-muted">
                <i class="fas fa-eye me-1"></i>
                <?php echo formatNumber($ad['views']); ?> مشاهدة
                
                <?php if ($ad['favorites'] > 0): ?>
                    <i class="fas fa-heart me-1 ms-2"></i>
                    <?php echo formatNumber($ad['favorites']); ?>
                <?php endif; ?>
                
                <?php if (!empty($ad['category_name'])): ?>
                    <i class="fas fa-tag me-1 ms-2"></i>
                    <a href="<?php echo url('category.php?id=' . $ad['category_id']); ?>" 
                       class="text-muted text-decoration-none">
                        <?php echo htmlspecialchars($ad['category_name']); ?>
                    </a>
                <?php endif; ?>
            </small>
        </div>
        
        <!-- أزرار الإجراءات -->
        <div class="ad-card-actions mt-3">
            <div class="btn-group w-100" role="group">
                <a href="<?php echo url('ad-details.php?id=' . $ad['id']); ?>" 
                   class="btn btn-primary btn-sm">
                    <i class="fas fa-eye me-1"></i>
                    عرض التفاصيل
                </a>
                
                <?php if (!empty($ad['contact_phone'])): ?>
                    <a href="tel:<?php echo $ad['contact_phone']; ?>" 
                       class="btn btn-success btn-sm">
                        <i class="fas fa-phone me-1"></i>
                        اتصال
                    </a>
                <?php endif; ?>
                
                <?php if (!empty($ad['whatsapp'])): ?>
                    <a href="https://wa.me/<?php echo preg_replace('/[^0-9]/', '', $ad['whatsapp']); ?>" 
                       target="_blank" 
                       class="btn btn-success btn-sm">
                        <i class="fab fa-whatsapp me-1"></i>
                        واتساب
                    </a>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- معلومات المعلن -->
        <?php if (!empty($ad['user_name'])): ?>
            <div class="ad-card-user mt-2">
                <small class="text-muted">
                    <i class="fas fa-user me-1"></i>
                    <?php echo htmlspecialchars($ad['user_name']); ?>
                </small>
            </div>
        <?php endif; ?>
    </div>
</div>

<style>
/* تحسينات إضافية لبطاقة الإعلان */
.ad-card-stats {
    border-top: 1px solid #eee;
    padding-top: 10px;
}

.ad-card-actions .btn-group {
    gap: 5px;
}

.ad-card-actions .btn {
    flex: 1;
    font-size: 0.8rem;
    padding: 5px 8px;
}

.ad-card-user {
    border-top: 1px solid #eee;
    padding-top: 8px;
}

.ad-card-favorite.active {
    color: #dc3545;
    background: rgba(220, 53, 69, 0.1);
}

.ad-card-favorite:hover {
    transform: scale(1.1);
}

/* تأثيرات الحركة */
.ad-card {
    transition: all 0.3s ease;
}

.ad-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.ad-card-image {
    overflow: hidden;
    border-radius: 8px 8px 0 0;
}

.ad-card-image img {
    transition: transform 0.3s ease;
}

.ad-card:hover .ad-card-image img {
    transform: scale(1.05);
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 576px) {
    .ad-card-actions .btn {
        font-size: 0.7rem;
        padding: 4px 6px;
    }
    
    .ad-card-actions .btn i {
        display: none;
    }
    
    .ad-card-meta {
        flex-direction: column;
        gap: 5px;
    }
    
    .ad-card-stats {
        font-size: 0.75rem;
    }
}

/* تحسين إمكانية الوصول */
.ad-card-favorite:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

.ad-card-title a:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
    text-decoration: underline;
}

/* تحسين الطباعة */
@media print {
    .ad-card-favorite,
    .ad-card-actions {
        display: none;
    }
    
    .ad-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ddd;
    }
}
</style>

<script>
// تحسين تجربة المستخدم
document.addEventListener('DOMContentLoaded', function() {
    // تأخير تحميل الصور
    const images = document.querySelectorAll('.ad-card-image img[loading="lazy"]');
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });
        
        images.forEach(img => imageObserver.observe(img));
    }
    
    // تتبع النقرات على الإعلانات
    document.querySelectorAll('.ad-card-title a').forEach(link => {
        link.addEventListener('click', function() {
            // إرسال إحصائية النقر
            const adId = this.href.match(/id=(\d+)/);
            if (adId) {
                fetch('<?php echo url("ajax/track-click.php"); ?>', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'ad_id=' + adId[1] + '&action=click'
                }).catch(err => console.log('تتبع النقر فشل:', err));
            }
        });
    });
});
</script>
