/**
 * ملف JavaScript خاص بلوحة الإدارة
 * Admin Panel JavaScript File
 */

// إعدادات عامة للوحة الإدارة
const AdminConfig = {
    baseUrl: window.location.origin + '/api/admin',
    ajaxTimeout: 30000,
    autoSaveInterval: 30000, // 30 ثانية
    refreshInterval: 300000, // 5 دقائق
    confirmDeleteMessage: 'هل أنت متأكد من الحذف؟ هذا الإجراء لا يمكن التراجع عنه.',
    confirmActionMessage: 'هل أنت متأكد من تنفيذ هذا الإجراء؟'
};

// فئة إدارة التنبيهات
class AdminAlerts {
    static show(message, type = 'info', duration = 5000) {
        const alertTypes = {
            'success': 'alert-success',
            'error': 'alert-danger',
            'warning': 'alert-warning',
            'info': 'alert-info'
        };
        
        const alertClass = alertTypes[type] || 'alert-info';
        const alertId = 'admin-alert-' + Date.now();
        
        const alertHtml = `
            <div id="${alertId}" class="alert ${alertClass} alert-dismissible fade show position-fixed" 
                 style="top: 90px; right: 20px; z-index: 9999; min-width: 350px; max-width: 500px;" role="alert">
                <i class="fas fa-${this.getIcon(type)} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', alertHtml);
        
        // إزالة التنبيه تلقائياً
        setTimeout(() => {
            const alert = document.getElementById(alertId);
            if (alert) {
                alert.remove();
            }
        }, duration);
    }
    
    static getIcon(type) {
        const icons = {
            'success': 'check-circle',
            'error': 'exclamation-triangle',
            'warning': 'exclamation-circle',
            'info': 'info-circle'
        };
        return icons[type] || 'info-circle';
    }
    
    static confirm(message, callback, title = 'تأكيد') {
        if (confirm(message)) {
            callback();
        }
    }
    
    static confirmDelete(callback, message = AdminConfig.confirmDeleteMessage) {
        this.confirm(message, callback, 'تأكيد الحذف');
    }
}

// فئة إدارة طلبات AJAX
class AdminAjax {
    static send(url, data = {}, method = 'POST', options = {}) {
        return new Promise((resolve, reject) => {
            const xhr = new XMLHttpRequest();
            xhr.timeout = options.timeout || AdminConfig.ajaxTimeout;
            
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            resolve(response);
                        } catch (e) {
                            reject(new Error('خطأ في تحليل الاستجابة'));
                        }
                    } else {
                        reject(new Error('خطأ في الشبكة: ' + xhr.status));
                    }
                }
            };
            
            xhr.ontimeout = function() {
                reject(new Error('انتهت مهلة الطلب'));
            };
            
            xhr.onerror = function() {
                reject(new Error('خطأ في الشبكة'));
            };
            
            // إضافة رمز CSRF تلقائياً
            if (method !== 'GET') {
                const csrfToken = document.querySelector('meta[name="csrf-token"]');
                if (csrfToken) {
                    data._token = csrfToken.getAttribute('content');
                }
            }
            
            if (method === 'GET') {
                const params = new URLSearchParams(data).toString();
                url += (url.includes('?') ? '&' : '?') + params;
                xhr.open('GET', url, true);
                xhr.send();
            } else {
                xhr.open(method, url, true);
                xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
                
                const formData = new URLSearchParams();
                for (const key in data) {
                    formData.append(key, data[key]);
                }
                
                xhr.send(formData.toString());
            }
        });
    }
    
    static get(url, data = {}) {
        return this.send(url, data, 'GET');
    }
    
    static post(url, data = {}) {
        return this.send(url, data, 'POST');
    }
    
    static put(url, data = {}) {
        return this.send(url, data, 'PUT');
    }
    
    static delete(url, data = {}) {
        return this.send(url, data, 'DELETE');
    }
}

// فئة إدارة النماذج
class AdminForms {
    static init() {
        // التحقق من صحة النماذج
        const forms = document.querySelectorAll('form[data-validate="true"]');
        forms.forEach(form => {
            form.addEventListener('submit', this.validateForm.bind(this));
        });
        
        // الحفظ التلقائي
        const autoSaveForms = document.querySelectorAll('form[data-autosave="true"]');
        autoSaveForms.forEach(form => {
            this.enableAutoSave(form);
        });
        
        // تأكيد الحذف
        const deleteButtons = document.querySelectorAll('[data-action="delete"]');
        deleteButtons.forEach(button => {
            button.addEventListener('click', this.handleDelete.bind(this));
        });
        
        // تأكيد الإجراءات
        const confirmButtons = document.querySelectorAll('[data-confirm="true"]');
        confirmButtons.forEach(button => {
            button.addEventListener('click', this.handleConfirm.bind(this));
        });
    }
    
    static validateForm(e) {
        const form = e.target;
        const requiredFields = form.querySelectorAll('[required]');
        let isValid = true;
        
        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                this.showFieldError(field, 'هذا الحقل مطلوب');
                isValid = false;
            } else {
                this.clearFieldError(field);
            }
        });
        
        // التحقق من البريد الإلكتروني
        const emailFields = form.querySelectorAll('input[type="email"]');
        emailFields.forEach(field => {
            if (field.value && !this.isValidEmail(field.value)) {
                this.showFieldError(field, 'البريد الإلكتروني غير صحيح');
                isValid = false;
            }
        });
        
        if (!isValid) {
            e.preventDefault();
            AdminAlerts.show('يرجى تصحيح الأخطاء في النموذج', 'error');
        }
    }
    
    static showFieldError(field, message) {
        this.clearFieldError(field);
        
        field.classList.add('is-invalid');
        const errorDiv = document.createElement('div');
        errorDiv.className = 'invalid-feedback';
        errorDiv.textContent = message;
        field.parentElement.appendChild(errorDiv);
    }
    
    static clearFieldError(field) {
        field.classList.remove('is-invalid');
        const errorDiv = field.parentElement.querySelector('.invalid-feedback');
        if (errorDiv) {
            errorDiv.remove();
        }
    }
    
    static isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    
    static enableAutoSave(form) {
        const inputs = form.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            input.addEventListener('input', () => {
                this.autoSave(form);
            });
        });
    }
    
    static autoSave(form) {
        const formData = new FormData(form);
        const data = {};
        for (let [key, value] of formData.entries()) {
            data[key] = value;
        }
        
        localStorage.setItem('admin_form_autosave_' + form.id, JSON.stringify(data));
        
        // إظهار مؤشر الحفظ التلقائي
        this.showAutoSaveIndicator();
    }
    
    static showAutoSaveIndicator() {
        const indicator = document.getElementById('autoSaveIndicator');
        if (indicator) {
            indicator.style.display = 'inline';
            setTimeout(() => {
                indicator.style.display = 'none';
            }, 2000);
        }
    }
    
    static handleDelete(e) {
        e.preventDefault();
        const button = e.target.closest('[data-action="delete"]');
        const url = button.getAttribute('href') || button.getAttribute('data-url');
        const message = button.getAttribute('data-message') || AdminConfig.confirmDeleteMessage;
        
        AdminAlerts.confirmDelete(() => {
            if (url) {
                window.location.href = url;
            }
        }, message);
    }
    
    static handleConfirm(e) {
        e.preventDefault();
        const button = e.target.closest('[data-confirm="true"]');
        const url = button.getAttribute('href') || button.getAttribute('data-url');
        const message = button.getAttribute('data-message') || AdminConfig.confirmActionMessage;
        
        AdminAlerts.confirm(message, () => {
            if (url) {
                window.location.href = url;
            }
        });
    }
}

// فئة إدارة الجداول
class AdminTables {
    static init() {
        // تفعيل الترتيب
        const sortableHeaders = document.querySelectorAll('th[data-sort]');
        sortableHeaders.forEach(header => {
            header.addEventListener('click', this.handleSort.bind(this));
            header.style.cursor = 'pointer';
            header.innerHTML += ' <i class="fas fa-sort text-muted"></i>';
        });
        
        // تفعيل التصفية
        const filterInputs = document.querySelectorAll('input[data-filter]');
        filterInputs.forEach(input => {
            input.addEventListener('input', this.handleFilter.bind(this));
        });
        
        // تحديد الكل
        const selectAllCheckboxes = document.querySelectorAll('input[data-select-all]');
        selectAllCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', this.handleSelectAll.bind(this));
        });
        
        // الإجراءات المجمعة
        const bulkActionButtons = document.querySelectorAll('[data-bulk-action]');
        bulkActionButtons.forEach(button => {
            button.addEventListener('click', this.handleBulkAction.bind(this));
        });
    }
    
    static handleSort(e) {
        const header = e.target.closest('th');
        const table = header.closest('table');
        const column = header.getAttribute('data-sort');
        const currentSort = table.getAttribute('data-current-sort');
        const currentOrder = table.getAttribute('data-current-order') || 'asc';
        
        let newOrder = 'asc';
        if (currentSort === column && currentOrder === 'asc') {
            newOrder = 'desc';
        }
        
        // تحديث URL مع معاملات الترتيب
        const url = new URL(window.location);
        url.searchParams.set('sort', column);
        url.searchParams.set('order', newOrder);
        window.location.href = url.toString();
    }
    
    static handleFilter(e) {
        const input = e.target;
        const column = input.getAttribute('data-filter');
        const value = input.value.toLowerCase();
        const table = input.closest('.table-container').querySelector('table');
        const rows = table.querySelectorAll('tbody tr');
        
        rows.forEach(row => {
            const cell = row.querySelector(`td[data-column="${column}"]`);
            if (cell) {
                const cellText = cell.textContent.toLowerCase();
                row.style.display = cellText.includes(value) ? '' : 'none';
            }
        });
    }
    
    static handleSelectAll(e) {
        const selectAll = e.target;
        const table = selectAll.closest('table');
        const checkboxes = table.querySelectorAll('tbody input[type="checkbox"]');
        
        checkboxes.forEach(checkbox => {
            checkbox.checked = selectAll.checked;
        });
        
        this.updateBulkActionButtons();
    }
    
    static handleBulkAction(e) {
        const button = e.target.closest('[data-bulk-action]');
        const action = button.getAttribute('data-bulk-action');
        const table = button.closest('.table-container').querySelector('table');
        const selectedCheckboxes = table.querySelectorAll('tbody input[type="checkbox"]:checked');
        
        if (selectedCheckboxes.length === 0) {
            AdminAlerts.show('يرجى تحديد عنصر واحد على الأقل', 'warning');
            return;
        }
        
        const ids = Array.from(selectedCheckboxes).map(cb => cb.value);
        const message = `هل أنت متأكد من تنفيذ "${action}" على ${ids.length} عنصر؟`;
        
        AdminAlerts.confirm(message, () => {
            this.executeBulkAction(action, ids);
        });
    }
    
    static executeBulkAction(action, ids) {
        AdminAjax.post(`${AdminConfig.baseUrl}/ajax/bulk-action.php`, {
            action: action,
            ids: ids
        })
        .then(response => {
            if (response.success) {
                AdminAlerts.show(response.message, 'success');
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                AdminAlerts.show(response.message || 'حدث خطأ', 'error');
            }
        })
        .catch(error => {
            AdminAlerts.show(error.message, 'error');
        });
    }
    
    static updateBulkActionButtons() {
        const tables = document.querySelectorAll('table');
        tables.forEach(table => {
            const selectedCount = table.querySelectorAll('tbody input[type="checkbox"]:checked').length;
            const bulkActions = table.closest('.table-container').querySelectorAll('[data-bulk-action]');
            
            bulkActions.forEach(button => {
                button.disabled = selectedCount === 0;
                const countSpan = button.querySelector('.selected-count');
                if (countSpan) {
                    countSpan.textContent = selectedCount;
                }
            });
        });
    }
}

// فئة إدارة الملفات
class AdminFileManager {
    static init() {
        // رفع الملفات بالسحب والإفلات
        const dropZones = document.querySelectorAll('.file-drop-zone');
        dropZones.forEach(zone => {
            this.initDropZone(zone);
        });
        
        // معاينة الصور
        const imageInputs = document.querySelectorAll('input[type="file"][accept*="image"]');
        imageInputs.forEach(input => {
            input.addEventListener('change', this.handleImagePreview.bind(this));
        });
    }
    
    static initDropZone(zone) {
        zone.addEventListener('dragover', (e) => {
            e.preventDefault();
            zone.classList.add('drag-over');
        });
        
        zone.addEventListener('dragleave', () => {
            zone.classList.remove('drag-over');
        });
        
        zone.addEventListener('drop', (e) => {
            e.preventDefault();
            zone.classList.remove('drag-over');
            
            const files = e.dataTransfer.files;
            this.handleFileUpload(files, zone);
        });
    }
    
    static handleFileUpload(files, zone) {
        const formData = new FormData();
        Array.from(files).forEach((file, index) => {
            formData.append(`files[${index}]`, file);
        });
        
        const uploadUrl = zone.getAttribute('data-upload-url');
        if (!uploadUrl) {
            AdminAlerts.show('لم يتم تحديد رابط الرفع', 'error');
            return;
        }
        
        // إظهار مؤشر التحميل
        zone.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin fa-2x"></i><br>جاري الرفع...</div>';
        
        fetch(uploadUrl, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                AdminAlerts.show('تم رفع الملفات بنجاح', 'success');
                this.displayUploadedFiles(data.files, zone);
            } else {
                AdminAlerts.show(data.message || 'فشل في رفع الملفات', 'error');
            }
        })
        .catch(error => {
            AdminAlerts.show('حدث خطأ أثناء رفع الملفات', 'error');
        })
        .finally(() => {
            // إعادة تعيين منطقة الإفلات
            zone.innerHTML = zone.getAttribute('data-original-content') || 'اسحب الملفات هنا أو انقر للاختيار';
        });
    }
    
    static handleImagePreview(e) {
        const input = e.target;
        const files = input.files;
        const previewContainer = input.parentElement.querySelector('.image-preview') || 
                               this.createPreviewContainer(input);
        
        previewContainer.innerHTML = '';
        
        Array.from(files).forEach((file, index) => {
            if (file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    const imageHtml = `
                        <div class="preview-item">
                            <img src="${e.target.result}" alt="معاينة الصورة" class="img-thumbnail">
                            <button type="button" class="btn btn-sm btn-danger remove-preview" 
                                    onclick="AdminFileManager.removePreview(this)">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    `;
                    previewContainer.insertAdjacentHTML('beforeend', imageHtml);
                };
                reader.readAsDataURL(file);
            }
        });
    }
    
    static createPreviewContainer(input) {
        const container = document.createElement('div');
        container.className = 'image-preview mt-3 d-flex flex-wrap gap-2';
        input.parentElement.appendChild(container);
        return container;
    }
    
    static removePreview(button) {
        const previewItem = button.closest('.preview-item');
        if (previewItem) {
            previewItem.remove();
        }
    }
}

// فئة إدارة الإحصائيات
class AdminStats {
    static init() {
        this.loadDashboardStats();
        this.initCharts();
        
        // تحديث الإحصائيات كل 5 دقائق
        setInterval(() => {
            this.loadDashboardStats();
        }, AdminConfig.refreshInterval);
    }
    
    static loadDashboardStats() {
        AdminAjax.get(`${AdminConfig.baseUrl}/ajax/dashboard-stats.php`)
            .then(response => {
                if (response.success) {
                    this.updateStatsCards(response.stats);
                }
            })
            .catch(error => {
                console.error('خطأ في تحميل الإحصائيات:', error);
            });
    }
    
    static updateStatsCards(stats) {
        Object.keys(stats).forEach(key => {
            const element = document.querySelector(`[data-stat="${key}"]`);
            if (element) {
                element.textContent = this.formatNumber(stats[key]);
            }
        });
    }
    
    static formatNumber(number) {
        return new Intl.NumberFormat('ar-SA').format(number);
    }
    
    static initCharts() {
        // يمكن إضافة تهيئة الرسوم البيانية هنا
    }
}

// تهيئة لوحة الإدارة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة جميع المكونات
    AdminForms.init();
    AdminTables.init();
    AdminFileManager.init();
    AdminStats.init();
    
    // تفعيل tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // تفعيل popovers
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    // مراقبة تغييرات الجداول
    document.addEventListener('change', function(e) {
        if (e.target.type === 'checkbox' && e.target.closest('table')) {
            AdminTables.updateBulkActionButtons();
        }
    });
    
    // حفظ حالة النماذج قبل مغادرة الصفحة
    window.addEventListener('beforeunload', function(e) {
        const forms = document.querySelectorAll('form[data-autosave="true"]');
        forms.forEach(form => {
            AdminForms.autoSave(form);
        });
    });
    
    // إخفاء التنبيهات تلقائياً
    setTimeout(() => {
        const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
        alerts.forEach(alert => {
            if (alert.classList.contains('show')) {
                alert.classList.remove('show');
                setTimeout(() => alert.remove(), 300);
            }
        });
    }, 5000);
});

// تصدير الكائنات للاستخدام العام
window.Admin = {
    Config: AdminConfig,
    Alerts: AdminAlerts,
    Ajax: AdminAjax,
    Forms: AdminForms,
    Tables: AdminTables,
    FileManager: AdminFileManager,
    Stats: AdminStats
};
