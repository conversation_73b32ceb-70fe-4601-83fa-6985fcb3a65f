# دليل النشر - Deployment Guide

## قائمة التحقق قبل النشر

### 1. إعدادات الأمان
- [ ] تغيير كلمة مرور المدير الافتراضية
- [ ] تحديث إعدادات قاعدة البيانات
- [ ] تفعيل HTTPS
- [ ] تحديث مفاتيح التشفير
- [ ] مراجعة صلاحيات الملفات

### 2. إعدادات الإنتاج
- [ ] تعطيل وضع التطوير (DEBUG_MODE = false)
- [ ] تحديث عنوان الموقع (SITE_URL)
- [ ] تكوين إعدادات البريد الإلكتروني
- [ ] تحديث معلومات الاتصال
- [ ] تحسين إعدادات قاعدة البيانات

### 3. الأداء
- [ ] تفعيل ضغط الملفات
- [ ] تحسين الصور
- [ ] تفعيل التخزين المؤقت
- [ ] تحسين استعلامات قاعدة البيانات

## خطوات النشر

### 1. تحضير الخادم

```bash
# تحديث النظام
sudo apt update && sudo apt upgrade -y

# تثبيت Apache
sudo apt install apache2 -y

# تثبيت PHP 8.x
sudo apt install php8.1 php8.1-mysql php8.1-gd php8.1-mbstring php8.1-curl php8.1-xml -y

# تثبيت MySQL
sudo apt install mysql-server -y

# تأمين MySQL
sudo mysql_secure_installation
```

### 2. تكوين Apache

```apache
# إنشاء Virtual Host
sudo nano /etc/apache2/sites-available/classified-ads.conf

<VirtualHost *:80>
    ServerName yourdomain.com
    ServerAlias www.yourdomain.com
    DocumentRoot /var/www/classified-ads
    
    <Directory /var/www/classified-ads>
        AllowOverride All
        Require all granted
    </Directory>
    
    ErrorLog ${APACHE_LOG_DIR}/classified-ads_error.log
    CustomLog ${APACHE_LOG_DIR}/classified-ads_access.log combined
</VirtualHost>

# تفعيل الموقع
sudo a2ensite classified-ads.conf
sudo a2enmod rewrite
sudo systemctl reload apache2
```

### 3. رفع الملفات

```bash
# نسخ الملفات إلى الخادم
scp -r classified-ads/ user@server:/var/www/

# تعيين الصلاحيات
sudo chown -R www-data:www-data /var/www/classified-ads
sudo chmod -R 755 /var/www/classified-ads
sudo chmod -R 777 /var/www/classified-ads/assets/uploads
sudo chmod -R 777 /var/www/classified-ads/logs
```

### 4. إعداد قاعدة البيانات

```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE classified_ads CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- إنشاء مستخدم قاعدة البيانات
CREATE USER 'classified_user'@'localhost' IDENTIFIED BY 'strong_password_here';
GRANT ALL PRIVILEGES ON classified_ads.* TO 'classified_user'@'localhost';
FLUSH PRIVILEGES;

-- استيراد البيانات
mysql -u classified_user -p classified_ads < database.sql
```

### 5. تحديث إعدادات الموقع

```php
// includes/config.php
define('DB_HOST', 'localhost');
define('DB_NAME', 'classified_ads');
define('DB_USER', 'classified_user');
define('DB_PASS', 'strong_password_here');

define('SITE_URL', 'https://yourdomain.com');
define('DEBUG_MODE', false);
define('LOG_ERRORS', true);

// تحديث مفاتيح الأمان
define('ENCRYPTION_KEY', 'your-unique-encryption-key-here');
define('HASH_SALT', 'your-unique-salt-here');
```

### 6. تفعيل SSL

```bash
# تثبيت Certbot
sudo apt install certbot python3-certbot-apache -y

# الحصول على شهادة SSL
sudo certbot --apache -d yourdomain.com -d www.yourdomain.com

# تجديد تلقائي
sudo crontab -e
# إضافة السطر التالي:
0 12 * * * /usr/bin/certbot renew --quiet
```

## إعدادات الأمان المتقدمة

### 1. جدار الحماية

```bash
# تفعيل UFW
sudo ufw enable

# السماح بـ SSH
sudo ufw allow ssh

# السماح بـ HTTP و HTTPS
sudo ufw allow 'Apache Full'

# عرض الحالة
sudo ufw status
```

### 2. حماية إضافية

```bash
# تثبيت Fail2Ban
sudo apt install fail2ban -y

# تكوين Fail2Ban
sudo nano /etc/fail2ban/jail.local

[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 3

[apache-auth]
enabled = true

[apache-badbots]
enabled = true

[apache-noscript]
enabled = true

[apache-overflows]
enabled = true

# إعادة تشغيل Fail2Ban
sudo systemctl restart fail2ban
```

### 3. تحديث كلمات المرور

```sql
-- تحديث كلمة مرور المدير
UPDATE admins SET password = '$2y$10$new_hashed_password_here' WHERE username = 'admin';
```

## مراقبة الأداء

### 1. إعداد المراقبة

```bash
# تثبيت htop
sudo apt install htop -y

# مراقبة Apache
sudo tail -f /var/log/apache2/access.log
sudo tail -f /var/log/apache2/error.log

# مراقبة MySQL
sudo tail -f /var/log/mysql/error.log
```

### 2. تحسين الأداء

```bash
# تحسين Apache
sudo nano /etc/apache2/apache2.conf

# إضافة الإعدادات التالية:
KeepAlive On
MaxKeepAliveRequests 100
KeepAliveTimeout 15

# تحسين MySQL
sudo nano /etc/mysql/mysql.conf.d/mysqld.cnf

# إضافة الإعدادات التالية:
innodb_buffer_pool_size = 256M
query_cache_size = 64M
query_cache_limit = 2M
```

## النسخ الاحتياطي

### 1. نسخ احتياطي تلقائي

```bash
#!/bin/bash
# backup.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/var/backups/classified-ads"
DB_NAME="classified_ads"
DB_USER="classified_user"
DB_PASS="password"

# إنشاء مجلد النسخ الاحتياطي
mkdir -p $BACKUP_DIR

# نسخ احتياطي لقاعدة البيانات
mysqldump -u $DB_USER -p$DB_PASS $DB_NAME > $BACKUP_DIR/db_$DATE.sql

# نسخ احتياطي للملفات
tar -czf $BACKUP_DIR/files_$DATE.tar.gz /var/www/classified-ads

# حذف النسخ القديمة (أكثر من 30 يوم)
find $BACKUP_DIR -name "*.sql" -mtime +30 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete

# جعل الملف قابل للتنفيذ
chmod +x backup.sh

# إضافة إلى crontab للتشغيل يومياً
crontab -e
# إضافة السطر التالي:
0 2 * * * /path/to/backup.sh
```

### 2. استعادة النسخ الاحتياطي

```bash
# استعادة قاعدة البيانات
mysql -u classified_user -p classified_ads < backup_file.sql

# استعادة الملفات
tar -xzf files_backup.tar.gz -C /
```

## مراقبة الأخطاء

### 1. ملفات السجل

```bash
# مراقبة أخطاء PHP
tail -f /var/www/classified-ads/logs/error.log

# مراقبة أخطاء Apache
tail -f /var/log/apache2/error.log

# مراقبة أخطاء MySQL
tail -f /var/log/mysql/error.log
```

### 2. إعداد التنبيهات

```bash
# تثبيت logwatch
sudo apt install logwatch -y

# تكوين logwatch
sudo nano /etc/logwatch/conf/logwatch.conf

# تعيين البريد الإلكتروني للتنبيهات
MailTo = <EMAIL>
Detail = High
```

## اختبار ما بعد النشر

### 1. اختبارات وظيفية
- [ ] تسجيل الدخول والخروج
- [ ] إضافة إعلان جديد
- [ ] رفع الصور
- [ ] البحث والتصفية
- [ ] إرسال الرسائل
- [ ] لوحة الإدارة

### 2. اختبارات الأداء
- [ ] سرعة تحميل الصفحات
- [ ] استجابة قاعدة البيانات
- [ ] ضغط الملفات
- [ ] التخزين المؤقت

### 3. اختبارات الأمان
- [ ] شهادة SSL
- [ ] حماية من SQL Injection
- [ ] حماية من XSS
- [ ] صلاحيات الملفات

## الصيانة الدورية

### يومياً
- مراجعة ملفات السجل
- مراقبة استخدام الموارد
- فحص النسخ الاحتياطي

### أسبوعياً
- تحديث النظام والبرامج
- تنظيف ملفات السجل القديمة
- مراجعة الأمان

### شهرياً
- تحليل الأداء
- مراجعة النسخ الاحتياطي
- تحديث كلمات المرور

---

**ملاحظة:** تأكد من اختبار جميع الوظائف في بيئة التطوير قبل النشر في الإنتاج.
